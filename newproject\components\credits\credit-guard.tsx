"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Coins, AlertCircle } from "lucide-react";
import { useAuth } from "@/components/auth/auth-provider";
import { getUserCredits } from "@/lib/supabase";

interface CreditGuardProps {
  children: React.ReactNode;
  requiredCredits?: number;
  onInsufficientCredits?: () => void;
}

export function CreditGuard({
  children,
  requiredCredits = 1,
  onInsufficientCredits,
}: CreditGuardProps) {
  const { user } = useAuth();
  const [credits, setCredits] = useState<number | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchCredits = async () => {
      if (!user) {
        setCredits(null);
        setLoading(false);
        return;
      }

      try {
        const userCredits = await getUserCredits(user.id);
        setCredits(userCredits);
      } catch (error) {
        console.error("Error fetching credits:", error);
        setCredits(0);
      } finally {
        setLoading(false);
      }
    };

    fetchCredits();
  }, [user]);

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!user) {
    return (
      <Card className="max-w-md mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-orange-500" />
            Authentication Required
          </CardTitle>
          <CardDescription>Please sign in to use this feature.</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  if (credits === null || credits < requiredCredits) {
    return (
      <Card className="max-w-md mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Coins className="h-5 w-5 text-yellow-600" />
            Insufficient Credits
          </CardTitle>
          <CardDescription>
            You need {requiredCredits} credit{requiredCredits > 1 ? "s" : ""} to
            use this feature. You currently have {credits || 0} credit
            {(credits || 0) !== 1 ? "s" : ""}.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={onInsufficientCredits} className="w-full">
            Get More Credits
          </Button>
        </CardContent>
      </Card>
    );
  }

  return <>{children}</>;
}
