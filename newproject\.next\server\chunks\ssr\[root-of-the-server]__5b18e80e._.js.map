{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/textb/newproject/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 19, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/textb/newproject/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { Slot } from \"@radix-ui/react-slot\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n);\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean;\n  }) {\n  const Comp = asChild ? Slot : \"button\";\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  );\n}\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/textb/newproject/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\";\n\nimport { cn } from \"@/lib/utils\";\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  );\n}\n\nexport { Input };\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/textb/newproject/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n));\nCard.displayName = \"Card\";\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n));\nCardHeader.displayName = \"CardHeader\";\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n));\nCardTitle.displayName = \"CardTitle\";\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n));\nCardDescription.displayName = \"CardDescription\";\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n));\nCardContent.displayName = \"CardContent\";\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n));\nCardFooter.displayName = \"CardFooter\";\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardDescription,\n  CardContent,\n};\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,qMAAA,CAAA,aAAgB,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,qMAAA,CAAA,aAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,qMAAA,CAAA,aAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,qMAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 201, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/textb/newproject/components/auth/auth-form.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport {\n  Card,\n  CardContent,\n  CardDescription,\n  CardHeader,\n  CardTitle,\n} from \"@/components/ui/card\";\nimport { signIn, signUp } from \"@/lib/supabase\";\nimport { toast } from \"sonner\";\n\ninterface AuthFormProps {\n  onSuccess?: () => void;\n}\n\nexport function AuthForm({ onSuccess }: AuthFormProps) {\n  const [isLogin, setIsLogin] = useState(true);\n  const [email, setEmail] = useState(\"\");\n  const [password, setPassword] = useState(\"\");\n  const [loading, setLoading] = useState(false);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n\n    try {\n      if (isLogin) {\n        const { error } = await signIn(email, password);\n        if (error) {\n          throw error;\n        }\n        toast.success(\"Signed in successfully!\");\n      } else {\n        const { error } = await signUp(email, password);\n        if (error) {\n          throw error;\n        }\n        toast.success(\"Account created! Please check your email to verify.\");\n      }\n      onSuccess?.();\n    } catch (error: unknown) {\n      const errorMessage =\n        error instanceof Error ? error.message : \"Authentication failed\";\n      toast.error(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Card className=\"w-full max-w-md mx-auto\">\n      <CardHeader>\n        <CardTitle>{isLogin ? \"Sign In\" : \"Sign Up\"}</CardTitle>\n        <CardDescription>\n          {isLogin\n            ? \"Welcome back!\"\n            : \"Create your account to get 3 free credits\"}\n        </CardDescription>\n      </CardHeader>\n      <CardContent>\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          <div>\n            <Input\n              type=\"email\"\n              placeholder=\"Email\"\n              value={email}\n              onChange={e => setEmail(e.target.value)}\n              required\n            />\n          </div>\n          <div>\n            <Input\n              type=\"password\"\n              placeholder=\"Password\"\n              value={password}\n              onChange={e => setPassword(e.target.value)}\n              required\n              minLength={6}\n            />\n          </div>\n          <Button type=\"submit\" className=\"w-full\" disabled={loading}>\n            {loading ? \"Loading...\" : isLogin ? \"Sign In\" : \"Sign Up\"}\n          </Button>\n        </form>\n        <div className=\"mt-4 text-center\">\n          <Button\n            variant=\"link\"\n            onClick={() => setIsLogin(!isLogin)}\n            className=\"text-sm\"\n          >\n            {isLogin\n              ? \"Need an account? Sign up\"\n              : \"Already have an account? Sign in\"}\n          </Button>\n        </div>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAOA;AACA;AAbA;;;;;;;;AAmBO,SAAS,SAAS,EAAE,SAAS,EAAiB;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QAEX,IAAI;YACF,IAAI,SAAS;gBACX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,+GAAA,CAAA,SAAM,AAAD,EAAE,OAAO;gBACtC,IAAI,OAAO;oBACT,MAAM;gBACR;gBACA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,+GAAA,CAAA,SAAM,AAAD,EAAE,OAAO;gBACtC,IAAI,OAAO;oBACT,MAAM;gBACR;gBACA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;YACA;QACF,EAAE,OAAO,OAAgB;YACvB,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC3C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC,yHAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,yHAAA,CAAA,aAAU;;kCACT,8OAAC,yHAAA,CAAA,YAAS;kCAAE,UAAU,YAAY;;;;;;kCAClC,8OAAC,yHAAA,CAAA,kBAAe;kCACb,UACG,kBACA;;;;;;;;;;;;0BAGR,8OAAC,yHAAA,CAAA,cAAW;;kCACV,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;0CACtC,8OAAC;0CACC,cAAA,8OAAC,0HAAA,CAAA,QAAK;oCACJ,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAA,IAAK,SAAS,EAAE,MAAM,CAAC,KAAK;oCACtC,QAAQ;;;;;;;;;;;0CAGZ,8OAAC;0CACC,cAAA,8OAAC,0HAAA,CAAA,QAAK;oCACJ,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAA,IAAK,YAAY,EAAE,MAAM,CAAC,KAAK;oCACzC,QAAQ;oCACR,WAAW;;;;;;;;;;;0CAGf,8OAAC,2HAAA,CAAA,SAAM;gCAAC,MAAK;gCAAS,WAAU;gCAAS,UAAU;0CAChD,UAAU,eAAe,UAAU,YAAY;;;;;;;;;;;;kCAGpD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,2HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS,IAAM,WAAW,CAAC;4BAC3B,WAAU;sCAET,UACG,6BACA;;;;;;;;;;;;;;;;;;;;;;;AAMhB", "debugId": null}}, {"offset": {"line": 365, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/textb/newproject/components/credits/credit-display.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect, useCallback } from \"react\";\nimport { Coins } from \"lucide-react\";\nimport { useAuth } from \"@/components/auth/auth-provider\";\nimport { getUserCredits } from \"@/lib/supabase\";\n\nexport function CreditDisplay() {\n  const { user } = useAuth();\n  const [credits, setCredits] = useState<number | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const fetchCredits = async () => {\n      if (!user) {\n        setCredits(null);\n        setLoading(false);\n        return;\n      }\n\n      try {\n        const userCredits = await getUserCredits(user.id);\n        setCredits(userCredits);\n      } catch (error) {\n        console.error(\"Error fetching credits:\", error);\n        setCredits(0);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchCredits();\n  }, [user]);\n\n  // Function to refresh credits (can be called from parent components)\n  const refreshCredits = useCallback(async () => {\n    if (!user) {\n      return;\n    }\n\n    try {\n      const userCredits = await getUserCredits(user.id);\n      setCredits(userCredits);\n    } catch (error) {\n      console.error(\"Error refreshing credits:\", error);\n    }\n  }, [user]);\n\n  // Expose refresh function to parent components\n  useEffect(() => {\n    // Store refresh function globally so other components can call it\n    if (typeof window !== \"undefined\") {\n      (window as unknown as { refreshCredits?: () => void }).refreshCredits =\n        refreshCredits;\n    }\n  }, [user, refreshCredits]);\n\n  if (!user || loading) {\n    return null;\n  }\n\n  return (\n    <div className=\"flex items-center gap-2 px-3 py-1.5 bg-muted rounded-lg\">\n      <Coins className=\"h-4 w-4 text-yellow-600\" />\n      <span className=\"text-sm font-medium\">\n        {credits !== null ? credits : \"...\"} credits\n      </span>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,IAAI,CAAC,MAAM;gBACT,WAAW;gBACX,WAAW;gBACX;YACF;YAEA,IAAI;gBACF,MAAM,cAAc,MAAM,CAAA,GAAA,+GAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,EAAE;gBAChD,WAAW;YACb,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,WAAW;YACb,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG;QAAC;KAAK;IAET,qEAAqE;IACrE,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,IAAI,CAAC,MAAM;YACT;QACF;QAEA,IAAI;YACF,MAAM,cAAc,MAAM,CAAA,GAAA,+GAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,EAAE;YAChD,WAAW;QACb,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF,GAAG;QAAC;KAAK;IAET,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,kEAAkE;QAClE;;IAIF,GAAG;QAAC;QAAM;KAAe;IAEzB,IAAI,CAAC,QAAQ,SAAS;QACpB,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;0BACjB,8OAAC;gBAAK,WAAU;;oBACb,YAAY,OAAO,UAAU;oBAAM;;;;;;;;;;;;;AAI5C", "debugId": null}}, {"offset": {"line": 462, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/textb/newproject/components/auth/user-menu.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { User, LogOut } from \"lucide-react\";\r\nimport { signOut } from \"@/lib/supabase\";\r\nimport { useAuth } from \"./auth-provider\";\r\nimport { CreditDisplay } from \"@/components/credits/credit-display\";\r\nimport { toast } from \"sonner\";\r\n\r\nexport function UserMenu() {\r\n  const { user } = useAuth();\r\n\r\n  const handleSignOut = async () => {\r\n    try {\r\n      await signOut();\r\n      toast.success(\"Signed out successfully\");\r\n    } catch {\r\n      toast.error(\"Error signing out\");\r\n    }\r\n  };\r\n\r\n  if (!user) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex items-center gap-3\">\r\n      <CreditDisplay />\r\n      <div className=\"flex items-center gap-2 text-sm\">\r\n        <User className=\"h-4 w-4\" />\r\n        <span className=\"hidden sm:inline\">{user.email}</span>\r\n      </div>\r\n      <Button variant=\"outline\" size=\"sm\" onClick={handleSignOut}>\r\n        <LogOut className=\"h-4 w-4\" />\r\n        <span className=\"hidden sm:inline ml-2\">Sign Out</span>\r\n      </Button>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AASO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD;IAEvB,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,CAAA,GAAA,+GAAA,CAAA,UAAO,AAAD;YACZ,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAM;YACN,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,2IAAA,CAAA,gBAAa;;;;;0BACd,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;kCAChB,8OAAC;wBAAK,WAAU;kCAAoB,KAAK,KAAK;;;;;;;;;;;;0BAEhD,8OAAC,2HAAA,CAAA,SAAM;gBAAC,SAAQ;gBAAU,MAAK;gBAAK,SAAS;;kCAC3C,8OAAC,0MAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,8OAAC;wBAAK,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;AAIhD", "debugId": null}}, {"offset": {"line": 563, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/textb/newproject/app/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { Upload } from \"lucide-react\";\r\nimport Link from \"next/link\";\r\nimport { AuthForm } from \"@/components/auth/auth-form\";\r\nimport { UserMenu } from \"@/components/auth/user-menu\";\r\nimport { useAuth } from \"@/components/auth/auth-provider\";\r\n\r\nexport default function LandingPage() {\r\n  const { user, loading } = useAuth();\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"min-h-screen bg-background flex items-center justify-center\">\r\n        <div className=\"text-center\">\r\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto\"></div>\r\n          <p className=\"mt-2 text-muted-foreground\">Loading...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n  return (\r\n    <div className=\"min-h-screen bg-background\">\r\n      {/* Header */}\r\n      <header className=\"border-b\">\r\n        <div className=\"container mx-auto px-4 py-4 flex justify-between items-center\">\r\n          <h1 className=\"text-xl font-bold\">Text Behind Image</h1>\r\n          {user && <UserMenu />}\r\n        </div>\r\n      </header>\r\n\r\n      {/* Main Content */}\r\n      {user ? (\r\n        // Authenticated user - show main app\r\n        <section className=\"py-20 px-4\">\r\n          <div className=\"container mx-auto text-center max-w-4xl\">\r\n            <div className=\"mb-8\">\r\n              <h1 className=\"text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent\">\r\n                Welcome back!\r\n              </h1>\r\n              <p className=\"text-xl text-muted-foreground mb-8 max-w-2xl mx-auto\">\r\n                Ready to create amazing text-behind-image effects? Start by\r\n                uploading an image.\r\n              </p>\r\n            </div>\r\n\r\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center mb-12\">\r\n              <Link href=\"/editor\">\r\n                <Button size=\"lg\" className=\"text-lg px-8 py-6\">\r\n                  <Upload className=\"mr-2 h-5 w-5\" />\r\n                  Start Creating\r\n                </Button>\r\n              </Link>\r\n            </div>\r\n          </div>\r\n        </section>\r\n      ) : (\r\n        // Not authenticated - show auth form\r\n        <section className=\"py-20 px-4\">\r\n          <div className=\"container mx-auto max-w-4xl\">\r\n            <div className=\"text-center mb-12\">\r\n              <h1 className=\"text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent\">\r\n                Create Stunning Text Behind Image Effects\r\n              </h1>\r\n              <p className=\"text-xl text-muted-foreground mb-8 max-w-2xl mx-auto\">\r\n                Transform your photos with AI-powered background removal and\r\n                place text behind objects for eye-catching designs.\r\n              </p>\r\n            </div>\r\n\r\n            <div className=\"grid md:grid-cols-2 gap-12 items-center\">\r\n              <div>\r\n                <AuthForm />\r\n              </div>\r\n              <div className=\"space-y-6\">\r\n                <div className=\"text-center md:text-left\">\r\n                  <h3 className=\"text-2xl font-semibold mb-4\">\r\n                    Get Started Free\r\n                  </h3>\r\n                  <ul className=\"space-y-3 text-muted-foreground\">\r\n                    <li className=\"flex items-center gap-2\">\r\n                      <div className=\"w-2 h-2 bg-primary rounded-full\"></div>3\r\n                      free credits to start\r\n                    </li>\r\n                    <li className=\"flex items-center gap-2\">\r\n                      <div className=\"w-2 h-2 bg-primary rounded-full\"></div>\r\n                      AI-powered background removal\r\n                    </li>\r\n                    <li className=\"flex items-center gap-2\">\r\n                      <div className=\"w-2 h-2 bg-primary rounded-full\"></div>\r\n                      Professional text effects\r\n                    </li>\r\n                    <li className=\"flex items-center gap-2\">\r\n                      <div className=\"w-2 h-2 bg-primary rounded-full\"></div>\r\n                      High-quality downloads\r\n                    </li>\r\n                  </ul>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </section>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AASe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD;IAEhC,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;;;;;;IAIlD;IACA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoB;;;;;;wBACjC,sBAAQ,8OAAC,mIAAA,CAAA,WAAQ;;;;;;;;;;;;;;;;YAKrB,OACC,qCAAqC;0BACrC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAgH;;;;;;8CAG9H,8OAAC;oCAAE,WAAU;8CAAuD;;;;;;;;;;;;sCAMtE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,8OAAC,2HAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,WAAU;;sDAC1B,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAQ7C,qCAAqC;0BACrC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAgH;;;;;;8CAG9H,8OAAC;oCAAE,WAAU;8CAAuD;;;;;;;;;;;;sCAMtE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CACC,cAAA,8OAAC,mIAAA,CAAA,WAAQ;;;;;;;;;;8CAEX,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA8B;;;;;;0DAG5C,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;;;;;;4DAAwC;;;;;;;kEAGzD,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;;;;;;4DAAwC;;;;;;;kEAGzD,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;;;;;;4DAAwC;;;;;;;kEAGzD,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;;;;;;4DAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY7E", "debugId": null}}]}