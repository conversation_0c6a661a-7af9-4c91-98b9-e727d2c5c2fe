"use client";

import { But<PERSON> } from "@/components/ui/button";
import { User, LogOut } from "lucide-react";
import { signOut } from "@/lib/supabase";
import { useAuth } from "./auth-provider";
import { CreditDisplay } from "@/components/credits/credit-display";
import { toast } from "sonner";

export function UserMenu() {
  const { user } = useAuth();

  const handleSignOut = async () => {
    try {
      await signOut();
      toast.success("Signed out successfully");
    } catch {
      toast.error("Error signing out");
    }
  };

  if (!user) {
    return null;
  }

  return (
    <div className="flex items-center gap-3">
      <CreditDisplay />
      <div className="flex items-center gap-2 text-sm">
        <User className="h-4 w-4" />
        <span className="hidden sm:inline">{user.email}</span>
      </div>
      <Button variant="outline" size="sm" onClick={handleSignOut}>
        <LogOut className="h-4 w-4" />
        <span className="hidden sm:inline ml-2">Sign Out</span>
      </Button>
    </div>
  );
}
