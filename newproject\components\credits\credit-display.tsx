"use client";

import { useState, useEffect, useCallback } from "react";
import { Coins } from "lucide-react";
import { useAuth } from "@/components/auth/auth-provider";
import { getUserCredits } from "@/lib/supabase";

export function CreditDisplay() {
  const { user } = useAuth();
  const [credits, setCredits] = useState<number | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchCredits = async () => {
      if (!user) {
        setCredits(null);
        setLoading(false);
        return;
      }

      try {
        const userCredits = await getUserCredits(user.id);
        setCredits(userCredits);
      } catch (error) {
        console.error("Error fetching credits:", error);
        setCredits(0);
      } finally {
        setLoading(false);
      }
    };

    fetchCredits();
  }, [user]);

  // Function to refresh credits (can be called from parent components)
  const refreshCredits = useCallback(async () => {
    if (!user) {
      return;
    }

    try {
      const userCredits = await getUserCredits(user.id);
      setCredits(userCredits);
    } catch (error) {
      console.error("Error refreshing credits:", error);
    }
  }, [user]);

  // Expose refresh function to parent components
  useEffect(() => {
    // Store refresh function globally so other components can call it
    if (typeof window !== "undefined") {
      (window as unknown as { refreshCredits?: () => void }).refreshCredits =
        refreshCredits;
    }
  }, [user, refreshCredits]);

  if (!user || loading) {
    return null;
  }

  return (
    <div className="flex items-center gap-2 px-3 py-1.5 bg-muted rounded-lg">
      <Coins className="h-4 w-4 text-yellow-600" />
      <span className="text-sm font-medium">
        {credits !== null ? credits : "..."} credits
      </span>
    </div>
  );
}
