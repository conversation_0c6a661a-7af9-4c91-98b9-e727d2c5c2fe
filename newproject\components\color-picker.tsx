"use client";

import { useEffect, useRef } from "react";
import { createPortal } from "react-dom";
import { SketchPicker } from "react-color";

interface ColorPickerProps {
  color: string;
  onChange: (color: string) => void;
  onClose: () => void;
  triggerRef?: React.RefObject<HTMLElement | null>;
}

export function ColorPicker({
  color,
  onChange,
  onClose,
  triggerRef,
}: ColorPickerProps) {
  const pickerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const positionPicker = () => {
      if (triggerRef?.current && pickerRef.current) {
        const triggerRect = triggerRef.current.getBoundingClientRect();
        const picker = pickerRef.current;
        const viewportHeight = window.innerHeight;
        const viewportWidth = window.innerWidth;

        // Approximate picker dimensions (SketchPicker is roughly 225x300)
        const pickerWidth = 225;
        const pickerHeight = 300;
        const spacing = 8;

        picker.style.position = "fixed";
        picker.style.zIndex = "9999";

        // Calculate horizontal position
        let leftPosition = triggerRect.left;

        // Adjust if picker would go off the right edge
        if (leftPosition + pickerWidth > viewportWidth) {
          leftPosition = viewportWidth - pickerWidth - 16; // 16px margin from edge
        }

        // Ensure it doesn't go off the left edge
        if (leftPosition < 16) {
          leftPosition = 16;
        }

        // Calculate vertical position
        let topPosition = triggerRect.bottom + spacing;

        // Check if picker would go off the bottom edge
        if (topPosition + pickerHeight > viewportHeight) {
          // Position above the trigger instead
          topPosition = triggerRect.top - pickerHeight - spacing;

          // If still not enough space above, position at the top with margin
          if (topPosition < 16) {
            topPosition = 16;
          }
        }

        picker.style.top = `${topPosition}px`;
        picker.style.left = `${leftPosition}px`;
      }
    };

    const handleClickOutside = (event: MouseEvent) => {
      if (
        pickerRef.current &&
        !pickerRef.current.contains(event.target as Node) &&
        triggerRef?.current &&
        !triggerRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    };

    const handleResize = () => {
      positionPicker();
    };

    const handleScroll = () => {
      positionPicker();
    };

    // Initial positioning
    positionPicker();

    document.addEventListener("mousedown", handleClickOutside);
    window.addEventListener("resize", handleResize);
    window.addEventListener("scroll", handleScroll, true); // Use capture to catch all scroll events

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      window.removeEventListener("resize", handleResize);
      window.removeEventListener("scroll", handleScroll, true);
    };
  }, [onClose, triggerRef]);

  const pickerElement = (
    <>
      {/* Mobile backdrop for easier closing */}
      <div
        className="fixed inset-0 bg-black/20 md:hidden"
        style={{ zIndex: 9998 }}
        onClick={onClose}
      />
      <div ref={pickerRef} className="relative">
        <SketchPicker
          color={color}
          onChange={color => onChange(color.hex)}
          disableAlpha={false}
        />
      </div>
    </>
  );

  // Use portal to render outside the current DOM tree
  return typeof window !== "undefined"
    ? createPortal(pickerElement, document.body)
    : null;
}
