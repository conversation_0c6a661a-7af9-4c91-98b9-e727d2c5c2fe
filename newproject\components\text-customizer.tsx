"use client";

import { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Slider } from "@/components/ui/slider";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Trash2,
  Copy,
  Palette,
  Type,
  Move,
  RotateCw,
  Sparkles,
} from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { TextCustomizerProps, TextSet } from "@/types";
import { FONT_OPTIONS } from "@/lib/constants";
import { ColorPicker } from "./color-picker";

export function TextCustomizer({
  textSet,
  onUpdate,
  onRemove,
  onDuplicate,
  imageWidth,
  imageHeight,
  isFirstText = false,
}: TextCustomizerProps) {
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [showShadowColorPicker, setShowShadowColorPicker] = useState(false);
  const [showOutlineColorPicker, setShowOutlineColorPicker] = useState(false);
  const [showGlowColorPicker, setShowGlowColorPicker] = useState(false);

  const colorPickerRef = useRef<HTMLDivElement>(null);
  const shadowColorPickerRef = useRef<HTMLDivElement>(null);
  const outlineColorPickerRef = useRef<HTMLDivElement>(null);
  const glowColorPickerRef = useRef<HTMLDivElement>(null);

  const handleAttributeChange = (
    attribute: keyof TextSet,
    value: string | number | boolean
  ) => {
    onUpdate(textSet.id, attribute, value);
  };

  return (
    <AccordionItem value={`text-${textSet.id}`} className="border rounded-lg">
      <div className="flex items-center justify-between px-3 sm:px-4 py-2 border-b">
        <div className="flex items-center gap-2 min-w-0 flex-1">
          <Type className="h-4 w-4 flex-shrink-0" />
          <span className="font-medium text-sm sm:text-base truncate">
            {textSet.text.length > 15
              ? `${textSet.text.substring(0, 15)}...`
              : textSet.text}
          </span>
        </div>
        <div className="flex items-center gap-1 sm:gap-2 flex-shrink-0">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onDuplicate(textSet.id)}
            className="h-8 w-8 p-0 sm:h-9 sm:w-9"
          >
            <Copy className="h-3 w-3 sm:h-4 sm:w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onRemove(textSet.id)}
            className="h-8 w-8 p-0 sm:h-9 sm:w-9"
          >
            <Trash2 className="h-3 w-3 sm:h-4 sm:w-4" />
          </Button>
        </div>
      </div>
      <AccordionTrigger className="px-3 sm:px-4 py-3 hover:no-underline">
        <span className="text-sm font-medium">Customize Text</span>
      </AccordionTrigger>

      <AccordionContent className="px-3 sm:px-4 pb-4">
        {/* Text Input - Always visible */}
        <div className="space-y-2 mb-4">
          <label className="text-sm font-medium">Text</label>
          <Input
            value={textSet.text}
            onChange={e => handleAttributeChange("text", e.target.value)}
            placeholder="Enter your text"
            className="text-base sm:text-sm"
          />
        </div>

        {/* Grouped Controls */}
        <Accordion
          type="single"
          collapsible
          className="w-full space-y-2"
          defaultValue={isFirstText ? "typography" : undefined}
        >
          {/* Typography Group */}
          <AccordionItem value="typography" className="border rounded-lg">
            <AccordionTrigger className="px-2 sm:px-3 py-2 text-sm font-medium">
              <div className="flex items-center gap-2">
                <Type className="h-4 w-4" />
                Typography
              </div>
            </AccordionTrigger>
            <AccordionContent className="px-2 sm:px-3 pb-3">
              <div className="space-y-3">
                {/* Font Family */}
                <div className="space-y-2">
                  <label className="text-xs font-medium">Font Family</label>
                  <Select
                    value={textSet.fontFamily}
                    onValueChange={value =>
                      handleAttributeChange("fontFamily", value)
                    }
                  >
                    <SelectTrigger className="w-full min-h-[44px] sm:min-h-[40px]">
                      <SelectValue>
                        <div className="flex items-center gap-2">
                          <Type className="h-4 w-4 flex-shrink-0" />
                          <span className="truncate">
                            {FONT_OPTIONS.find(
                              f => f.value === textSet.fontFamily
                            )?.label || "Select Font"}
                          </span>
                        </div>
                      </SelectValue>
                    </SelectTrigger>
                    <SelectContent className="max-h-[300px]">
                      {FONT_OPTIONS.map(font => (
                        <SelectItem
                          key={font.value}
                          value={font.value}
                          className="cursor-pointer"
                        >
                          <span
                            className="font-medium text-sm"
                            style={{ fontFamily: font.value }}
                          >
                            {font.label}
                          </span>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Font Size */}
                <div className="space-y-3">
                  <label className="text-xs text-muted-foreground">
                    Font Size: {textSet.fontSize}px
                  </label>
                  <Slider
                    value={[textSet.fontSize]}
                    onValueChange={([value]) =>
                      handleAttributeChange("fontSize", value)
                    }
                    min={50}
                    max={1500}
                    step={1}
                    className="w-full touch-manipulation"
                  />
                </div>

                {/* Font Weight */}
                <div className="space-y-3">
                  <label className="text-xs text-muted-foreground">
                    Font Weight: {textSet.fontWeight}
                  </label>
                  <Slider
                    value={[textSet.fontWeight]}
                    onValueChange={([value]) =>
                      handleAttributeChange("fontWeight", value)
                    }
                    min={100}
                    max={900}
                    step={100}
                    className="w-full touch-manipulation"
                  />
                </div>

                {/* Letter Spacing */}
                <div className="space-y-3">
                  <label className="text-xs text-muted-foreground">
                    Letter Spacing: {textSet.letterSpacing}px
                  </label>
                  <Slider
                    value={[textSet.letterSpacing]}
                    onValueChange={([value]) =>
                      handleAttributeChange("letterSpacing", value)
                    }
                    min={-10}
                    max={60}
                    step={1}
                    className="w-full touch-manipulation"
                  />
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>

          {/* Color Group */}
          <AccordionItem value="color" className="border rounded-lg">
            <AccordionTrigger className="px-2 sm:px-3 py-2 text-sm font-medium">
              <div className="flex items-center gap-2">
                <Palette className="h-4 w-4" />
                Color & Opacity
              </div>
            </AccordionTrigger>
            <AccordionContent className="px-2 sm:px-3 pb-3">
              <div className="space-y-3">
                {/* Text Color */}
                <div className="space-y-2 relative">
                  <label className="text-xs font-medium">Text Color</label>
                  <div className="flex items-center gap-2">
                    <div
                      ref={colorPickerRef}
                      className="w-10 h-10 sm:w-8 sm:h-8 rounded border cursor-pointer flex-shrink-0"
                      style={{ backgroundColor: textSet.color }}
                      onClick={() => setShowColorPicker(!showColorPicker)}
                    />
                    <Input
                      value={textSet.color}
                      onChange={e =>
                        handleAttributeChange("color", e.target.value)
                      }
                      placeholder="#ffffff"
                      className="flex-1 text-base sm:text-sm min-h-[44px] sm:min-h-[40px]"
                    />
                  </div>
                  {showColorPicker && (
                    <ColorPicker
                      color={textSet.color}
                      onChange={(color: string) =>
                        handleAttributeChange("color", color)
                      }
                      onClose={() => setShowColorPicker(false)}
                      triggerRef={colorPickerRef}
                    />
                  )}
                </div>

                {/* Opacity */}
                <div className="space-y-3">
                  <label className="text-xs text-muted-foreground">
                    Opacity: {Math.round(textSet.opacity * 100)}%
                  </label>
                  <Slider
                    value={[textSet.opacity]}
                    onValueChange={([value]) =>
                      handleAttributeChange("opacity", value)
                    }
                    min={0}
                    max={1}
                    step={0.01}
                    className="w-full touch-manipulation"
                  />
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>

          {/* Position Group */}
          <AccordionItem value="position" className="border rounded-lg">
            <AccordionTrigger className="px-2 sm:px-3 py-2 text-sm font-medium">
              <div className="flex items-center gap-2">
                <Move className="h-4 w-4" />
                Position
              </div>
            </AccordionTrigger>
            <AccordionContent className="px-2 sm:px-3 pb-3">
              <div className="space-y-4">
                <div className="space-y-3">
                  <label className="text-xs text-muted-foreground">
                    X Position: {textSet.x}px
                  </label>
                  <Slider
                    value={[textSet.x]}
                    onValueChange={([value]) =>
                      handleAttributeChange("x", value)
                    }
                    min={0}
                    max={imageWidth}
                    step={1}
                    className="w-full touch-manipulation"
                  />
                </div>

                <div className="space-y-3">
                  <label className="text-xs text-muted-foreground">
                    Y Position: {textSet.y}px
                  </label>
                  <Slider
                    value={[textSet.y]}
                    onValueChange={([value]) =>
                      handleAttributeChange("y", value)
                    }
                    min={0}
                    max={imageHeight}
                    step={1}
                    className="w-full touch-manipulation"
                  />
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>

          {/* Transform Group */}
          <AccordionItem value="transform" className="border rounded-lg">
            <AccordionTrigger className="px-3 py-2 text-sm font-medium">
              <div className="flex items-center gap-2">
                <RotateCw className="h-4 w-4" />
                Transform
              </div>
            </AccordionTrigger>
            <AccordionContent className="px-3 pb-3">
              <div className="space-y-3">
                <div className="space-y-2">
                  <label className="text-xs text-muted-foreground">
                    Rotation: {textSet.rotation}°
                  </label>
                  <Slider
                    value={[textSet.rotation]}
                    onValueChange={([value]) =>
                      handleAttributeChange("rotation", value)
                    }
                    min={-180}
                    max={180}
                    step={1}
                    className="w-full"
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-xs text-muted-foreground">
                    Tilt X: {textSet.tiltX}°
                  </label>
                  <Slider
                    value={[textSet.tiltX]}
                    onValueChange={([value]) =>
                      handleAttributeChange("tiltX", value)
                    }
                    min={-45}
                    max={45}
                    step={1}
                    className="w-full"
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-xs text-muted-foreground">
                    Tilt Y: {textSet.tiltY}°
                  </label>
                  <Slider
                    value={[textSet.tiltY]}
                    onValueChange={([value]) =>
                      handleAttributeChange("tiltY", value)
                    }
                    min={-45}
                    max={45}
                    step={1}
                    className="w-full"
                  />
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>

          {/* Text Effects Group */}
          <AccordionItem value="effects" className="border rounded-lg">
            <AccordionTrigger className="px-3 py-2 text-sm font-medium">
              <div className="flex items-center gap-2">
                <Sparkles className="h-4 w-4" />
                Text Effects
              </div>
            </AccordionTrigger>
            <AccordionContent className="px-3 pb-3">
              <div className="space-y-4 mb-2">
                {/* Shadow Effect */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <label className="text-xs font-medium">Drop Shadow</label>
                    <Switch
                      checked={textSet.shadowEnabled}
                      onCheckedChange={checked =>
                        handleAttributeChange("shadowEnabled", checked)
                      }
                    />
                  </div>
                </div>
                {textSet.shadowEnabled && (
                  <div className="space-y-3 pl-4 border-l-2 border-muted">
                    <div className="space-y-2">
                      <label className="text-xs text-muted-foreground">
                        Shadow Color
                      </label>
                      <div className="flex items-center gap-2">
                        <div
                          ref={shadowColorPickerRef}
                          className="w-6 h-6 rounded border cursor-pointer"
                          style={{ backgroundColor: textSet.shadowColor }}
                          onClick={() =>
                            setShowShadowColorPicker(!showShadowColorPicker)
                          }
                        />
                        <Input
                          value={textSet.shadowColor}
                          onChange={e =>
                            handleAttributeChange("shadowColor", e.target.value)
                          }
                          placeholder="#000000"
                          className="flex-1 text-xs"
                        />
                      </div>
                      {showShadowColorPicker && (
                        <ColorPicker
                          color={textSet.shadowColor}
                          onChange={(color: string) =>
                            handleAttributeChange("shadowColor", color)
                          }
                          onClose={() => setShowShadowColorPicker(false)}
                          triggerRef={shadowColorPickerRef}
                        />
                      )}
                    </div>

                    <div className="space-y-2">
                      <label className="text-xs text-muted-foreground">
                        Blur: {textSet.shadowBlur}px
                      </label>
                      <Slider
                        value={[textSet.shadowBlur]}
                        onValueChange={([value]) =>
                          handleAttributeChange("shadowBlur", value)
                        }
                        min={0}
                        max={20}
                        step={1}
                        className="w-full"
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-2">
                      <div className="space-y-2">
                        <label className="text-xs text-muted-foreground">
                          X: {textSet.shadowOffsetX}px
                        </label>
                        <Slider
                          value={[textSet.shadowOffsetX]}
                          onValueChange={([value]) =>
                            handleAttributeChange("shadowOffsetX", value)
                          }
                          min={-20}
                          max={20}
                          step={1}
                          className="w-full"
                        />
                      </div>

                      <div className="space-y-2">
                        <label className="text-xs text-muted-foreground">
                          Y: {textSet.shadowOffsetY}px
                        </label>
                        <Slider
                          value={[textSet.shadowOffsetY]}
                          onValueChange={([value]) =>
                            handleAttributeChange("shadowOffsetY", value)
                          }
                          min={-20}
                          max={20}
                          step={1}
                          className="w-full"
                        />
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Outline Effect */}
              <div className="space-y-3 mb-2">
                <div className="flex items-center justify-between">
                  <label className="text-xs font-medium">Text Outline</label>
                  <Switch
                    checked={textSet.outlineEnabled}
                    onCheckedChange={checked =>
                      handleAttributeChange("outlineEnabled", checked)
                    }
                  />
                </div>

                {textSet.outlineEnabled && (
                  <div className="space-y-3 pl-4 border-l-2 border-muted">
                    <div className="space-y-2">
                      <label className="text-xs text-muted-foreground">
                        Outline Color
                      </label>
                      <div className="flex items-center gap-2">
                        <div
                          ref={outlineColorPickerRef}
                          className="w-6 h-6 rounded border cursor-pointer"
                          style={{ backgroundColor: textSet.outlineColor }}
                          onClick={() =>
                            setShowOutlineColorPicker(!showOutlineColorPicker)
                          }
                        />
                        <Input
                          value={textSet.outlineColor}
                          onChange={e =>
                            handleAttributeChange(
                              "outlineColor",
                              e.target.value
                            )
                          }
                          placeholder="#000000"
                          className="flex-1 text-xs"
                        />
                      </div>
                      {showOutlineColorPicker && (
                        <ColorPicker
                          color={textSet.outlineColor}
                          onChange={(color: string) =>
                            handleAttributeChange("outlineColor", color)
                          }
                          onClose={() => setShowOutlineColorPicker(false)}
                          triggerRef={outlineColorPickerRef}
                        />
                      )}
                    </div>

                    <div className="space-y-2">
                      <label className="text-xs text-muted-foreground">
                        Width: {textSet.outlineWidth}px
                      </label>
                      <Slider
                        value={[textSet.outlineWidth]}
                        onValueChange={([value]) =>
                          handleAttributeChange("outlineWidth", value)
                        }
                        min={1}
                        max={100}
                        step={1}
                        className="w-full"
                      />
                    </div>
                  </div>
                )}
              </div>

              {/* Glow Effect */}
              <div className="space-y-3 mb-2">
                <div className="flex items-center justify-between">
                  <label className="text-xs font-medium">Text Glow</label>
                  <Switch
                    checked={textSet.glowEnabled}
                    onCheckedChange={checked =>
                      handleAttributeChange("glowEnabled", checked)
                    }
                  />
                </div>

                {textSet.glowEnabled && (
                  <div className="space-y-3 pl-4 border-l-2 border-muted">
                    <div className="space-y-2">
                      <label className="text-xs text-muted-foreground">
                        Glow Color
                      </label>
                      <div className="flex items-center gap-2">
                        <div
                          ref={glowColorPickerRef}
                          className="w-6 h-6 rounded border cursor-pointer"
                          style={{ backgroundColor: textSet.glowColor }}
                          onClick={() =>
                            setShowGlowColorPicker(!showGlowColorPicker)
                          }
                        />
                        <Input
                          value={textSet.glowColor}
                          onChange={e =>
                            handleAttributeChange("glowColor", e.target.value)
                          }
                          placeholder="#ffffff"
                          className="flex-1 text-xs"
                        />
                      </div>
                      {showGlowColorPicker && (
                        <ColorPicker
                          color={textSet.glowColor}
                          onChange={(color: string) =>
                            handleAttributeChange("glowColor", color)
                          }
                          onClose={() => setShowGlowColorPicker(false)}
                          triggerRef={glowColorPickerRef}
                        />
                      )}
                    </div>

                    <div className="space-y-2">
                      <label className="text-xs text-muted-foreground">
                        Intensity: {textSet.glowIntensity}px
                      </label>
                      <Slider
                        value={[textSet.glowIntensity]}
                        onValueChange={([value]) =>
                          handleAttributeChange("glowIntensity", value)
                        }
                        min={1}
                        max={50}
                        step={1}
                        className="w-full"
                      />
                    </div>
                  </div>
                )}
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </AccordionContent>
    </AccordionItem>
  );
}
