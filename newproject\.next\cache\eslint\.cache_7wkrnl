[{"C:\\Users\\<USER>\\Desktop\\textb\\newproject\\app\\layout.tsx": "1", "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\app\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\color-picker.tsx": "3", "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\text-customizer.tsx": "4", "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\ui\\accordion.tsx": "5", "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\ui\\button.tsx": "6", "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\ui\\dialog.tsx": "7", "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\ui\\input.tsx": "8", "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\ui\\scroll-area.tsx": "9", "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\ui\\separator.tsx": "10", "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\ui\\slider.tsx": "11", "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\ui\\sonner.tsx": "12", "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\lib\\utils.ts": "13", "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\app\\editor\\page.tsx": "14", "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\ui\\skeleton.tsx": "15", "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\lib\\constants.ts": "16", "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\lib\\image-utils.ts": "17", "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\ui\\switch.tsx": "18", "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\layer-composition.tsx": "19", "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\ui\\select.tsx": "20", "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\ui\\card.tsx": "21", "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\auth\\auth-form.tsx": "22", "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\auth\\auth-provider.tsx": "23", "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\auth\\user-menu.tsx": "24", "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\lib\\supabase.ts": "25", "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\credits\\credit-display.tsx": "26", "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\credits\\credit-guard.tsx": "27", "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\app\\api\\webhooks\\lemonsqueezy\\route.ts": "28", "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\app\\dashboard\\page.tsx": "29", "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\app\\pricing\\page.tsx": "30", "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\dashboard\\subscription-status.tsx": "31", "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\pricing\\pricing-card.tsx": "32", "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\ui\\badge.tsx": "33", "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\lib\\lemonsqueezy.ts": "34"}, {"size": 4282, "mtime": 1754055102341, "results": "35", "hashOfConfig": "36"}, {"size": 4546, "mtime": 1754055102343, "results": "37", "hashOfConfig": "36"}, {"size": 3578, "mtime": 1753815164873, "results": "38", "hashOfConfig": "36"}, {"size": 24955, "mtime": 1753961123059, "results": "39", "hashOfConfig": "36"}, {"size": 2063, "mtime": 1753780777465, "results": "40", "hashOfConfig": "36"}, {"size": 2132, "mtime": 1753780777477, "results": "41", "hashOfConfig": "36"}, {"size": 3999, "mtime": 1753780777503, "results": "42", "hashOfConfig": "36"}, {"size": 971, "mtime": 1753780777508, "results": "43", "hashOfConfig": "36"}, {"size": 1652, "mtime": 1753780777524, "results": "44", "hashOfConfig": "36"}, {"size": 705, "mtime": 1753780777538, "results": "45", "hashOfConfig": "36"}, {"size": 2008, "mtime": 1753780777564, "results": "46", "hashOfConfig": "36"}, {"size": 722, "mtime": 1753816320783, "results": "47", "hashOfConfig": "36"}, {"size": 169, "mtime": 1753780777642, "results": "48", "hashOfConfig": "36"}, {"size": 16306, "mtime": 1754055102337, "results": "49", "hashOfConfig": "36"}, {"size": 279, "mtime": 1753780777550, "results": "50", "hashOfConfig": "36"}, {"size": 2456, "mtime": 1753948919185, "results": "51", "hashOfConfig": "36"}, {"size": 7794, "mtime": 1753961374017, "results": "52", "hashOfConfig": "36"}, {"size": 1169, "mtime": 1753809572678, "results": "53", "hashOfConfig": "36"}, {"size": 7876, "mtime": 1753961373713, "results": "54", "hashOfConfig": "36"}, {"size": 6270, "mtime": 1753947289039, "results": "55", "hashOfConfig": "36"}, {"size": 1991, "mtime": 1754055102374, "results": "56", "hashOfConfig": "36"}, {"size": 2946, "mtime": 1754055102350, "results": "57", "hashOfConfig": "36"}, {"size": 1333, "mtime": 1754055102353, "results": "58", "hashOfConfig": "36"}, {"size": 1334, "mtime": 1754055102356, "results": "59", "hashOfConfig": "36"}, {"size": 2520, "mtime": 1754055102380, "results": "60", "hashOfConfig": "36"}, {"size": 1925, "mtime": 1754055102359, "results": "61", "hashOfConfig": "36"}, {"size": 2727, "mtime": 1754055102361, "results": "62", "hashOfConfig": "36"}, {"size": 5020, "mtime": 1754055102329, "results": "63", "hashOfConfig": "36"}, {"size": 2679, "mtime": 1754055102332, "results": "64", "hashOfConfig": "36"}, {"size": 4297, "mtime": 1754055102348, "results": "65", "hashOfConfig": "36"}, {"size": 3623, "mtime": 1754055102365, "results": "66", "hashOfConfig": "36"}, {"size": 2501, "mtime": 1754061324730, "results": "67", "hashOfConfig": "36"}, {"size": 1164, "mtime": 1754055102372, "results": "68", "hashOfConfig": "36"}, {"size": 2006, "mtime": 1754055102376, "results": "69", "hashOfConfig": "36"}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "19znye1", {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\color-picker.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\text-customizer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\ui\\accordion.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\ui\\dialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\ui\\scroll-area.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\ui\\separator.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\ui\\slider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\ui\\sonner.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\app\\editor\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\ui\\skeleton.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\lib\\constants.ts", [], [], "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\lib\\image-utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\ui\\switch.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\layer-composition.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\ui\\select.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\auth\\auth-form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\auth\\auth-provider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\auth\\user-menu.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\lib\\supabase.ts", [], [], "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\credits\\credit-display.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\credits\\credit-guard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\app\\api\\webhooks\\lemonsqueezy\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\app\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\app\\pricing\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\dashboard\\subscription-status.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\pricing\\pricing-card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\textb\\newproject\\lib\\lemonsqueezy.ts", [], []]