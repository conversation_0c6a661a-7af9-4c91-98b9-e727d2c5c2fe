(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@radix-ui/react-compose-refs/dist/index.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// packages/react/compose-refs/src/compose-refs.tsx
__turbopack_context__.s({
    "composeRefs": ()=>composeRefs,
    "useComposedRefs": ()=>useComposedRefs
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
function setRef(ref, value) {
    if (typeof ref === "function") {
        return ref(value);
    } else if (ref !== null && ref !== void 0) {
        ref.current = value;
    }
}
function composeRefs() {
    for(var _len = arguments.length, refs = new Array(_len), _key = 0; _key < _len; _key++){
        refs[_key] = arguments[_key];
    }
    return (node)=>{
        let hasCleanup = false;
        const cleanups = refs.map((ref)=>{
            const cleanup = setRef(ref, node);
            if (!hasCleanup && typeof cleanup == "function") {
                hasCleanup = true;
            }
            return cleanup;
        });
        if (hasCleanup) {
            return ()=>{
                for(let i = 0; i < cleanups.length; i++){
                    const cleanup = cleanups[i];
                    if (typeof cleanup == "function") {
                        cleanup();
                    } else {
                        setRef(refs[i], null);
                    }
                }
            };
        }
    };
}
function useComposedRefs() {
    for(var _len = arguments.length, refs = new Array(_len), _key = 0; _key < _len; _key++){
        refs[_key] = arguments[_key];
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"](composeRefs(...refs), refs);
}
;
 //# sourceMappingURL=index.mjs.map
}),
"[project]/node_modules/@radix-ui/react-slot/dist/index.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// src/slot.tsx
__turbopack_context__.s({
    "Root": ()=>Slot,
    "Slot": ()=>Slot,
    "Slottable": ()=>Slottable,
    "createSlot": ()=>createSlot,
    "createSlottable": ()=>createSlottable
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$compose$2d$refs$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-compose-refs/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
;
;
;
// @__NO_SIDE_EFFECTS__
function createSlot(ownerName) {
    const SlotClone = /* @__PURE__ */ createSlotClone(ownerName);
    const Slot2 = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"]((props, forwardedRef)=>{
        const { children, ...slotProps } = props;
        const childrenArray = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Children"].toArray(children);
        const slottable = childrenArray.find(isSlottable);
        if (slottable) {
            const newElement = slottable.props.children;
            const newChildren = childrenArray.map((child)=>{
                if (child === slottable) {
                    if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Children"].count(newElement) > 1) return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Children"].only(null);
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidElement"](newElement) ? newElement.props.children : null;
                } else {
                    return child;
                }
            });
            return /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(SlotClone, {
                ...slotProps,
                ref: forwardedRef,
                children: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidElement"](newElement) ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cloneElement"](newElement, void 0, newChildren) : null
            });
        }
        return /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(SlotClone, {
            ...slotProps,
            ref: forwardedRef,
            children
        });
    });
    Slot2.displayName = "".concat(ownerName, ".Slot");
    return Slot2;
}
var Slot = /* @__PURE__ */ createSlot("Slot");
// @__NO_SIDE_EFFECTS__
function createSlotClone(ownerName) {
    const SlotClone = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"]((props, forwardedRef)=>{
        const { children, ...slotProps } = props;
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidElement"](children)) {
            const childrenRef = getElementRef(children);
            const props2 = mergeProps(slotProps, children.props);
            if (children.type !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"]) {
                props2.ref = forwardedRef ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$compose$2d$refs$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["composeRefs"])(forwardedRef, childrenRef) : childrenRef;
            }
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cloneElement"](children, props2);
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Children"].count(children) > 1 ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Children"].only(null) : null;
    });
    SlotClone.displayName = "".concat(ownerName, ".SlotClone");
    return SlotClone;
}
var SLOTTABLE_IDENTIFIER = Symbol("radix.slottable");
// @__NO_SIDE_EFFECTS__
function createSlottable(ownerName) {
    const Slottable2 = (param)=>{
        let { children } = param;
        return /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
            children
        });
    };
    Slottable2.displayName = "".concat(ownerName, ".Slottable");
    Slottable2.__radixId = SLOTTABLE_IDENTIFIER;
    return Slottable2;
}
var Slottable = /* @__PURE__ */ createSlottable("Slottable");
function isSlottable(child) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidElement"](child) && typeof child.type === "function" && "__radixId" in child.type && child.type.__radixId === SLOTTABLE_IDENTIFIER;
}
function mergeProps(slotProps, childProps) {
    const overrideProps = {
        ...childProps
    };
    for(const propName in childProps){
        const slotPropValue = slotProps[propName];
        const childPropValue = childProps[propName];
        const isHandler = /^on[A-Z]/.test(propName);
        if (isHandler) {
            if (slotPropValue && childPropValue) {
                overrideProps[propName] = function() {
                    for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){
                        args[_key] = arguments[_key];
                    }
                    const result = childPropValue(...args);
                    slotPropValue(...args);
                    return result;
                };
            } else if (slotPropValue) {
                overrideProps[propName] = slotPropValue;
            }
        } else if (propName === "style") {
            overrideProps[propName] = {
                ...slotPropValue,
                ...childPropValue
            };
        } else if (propName === "className") {
            overrideProps[propName] = [
                slotPropValue,
                childPropValue
            ].filter(Boolean).join(" ");
        }
    }
    return {
        ...slotProps,
        ...overrideProps
    };
}
function getElementRef(element) {
    var _Object_getOwnPropertyDescriptor, _Object_getOwnPropertyDescriptor1;
    let getter = (_Object_getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor(element.props, "ref")) === null || _Object_getOwnPropertyDescriptor === void 0 ? void 0 : _Object_getOwnPropertyDescriptor.get;
    let mayWarn = getter && "isReactWarning" in getter && getter.isReactWarning;
    if (mayWarn) {
        return element.ref;
    }
    getter = (_Object_getOwnPropertyDescriptor1 = Object.getOwnPropertyDescriptor(element, "ref")) === null || _Object_getOwnPropertyDescriptor1 === void 0 ? void 0 : _Object_getOwnPropertyDescriptor1.get;
    mayWarn = getter && "isReactWarning" in getter && getter.isReactWarning;
    if (mayWarn) {
        return element.props.ref;
    }
    return element.props.ref || element.ref;
}
;
 //# sourceMappingURL=index.mjs.map
}),
"[project]/node_modules/clsx/dist/clsx.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "clsx": ()=>clsx,
    "default": ()=>__TURBOPACK__default__export__
});
function r(e) {
    var t, f, n = "";
    if ("string" == typeof e || "number" == typeof e) n += e;
    else if ("object" == typeof e) if (Array.isArray(e)) {
        var o = e.length;
        for(t = 0; t < o; t++)e[t] && (f = r(e[t])) && (n && (n += " "), n += f);
    } else for(f in e)e[f] && (n && (n += " "), n += f);
    return n;
}
function clsx() {
    for(var e, t, f = 0, n = "", o = arguments.length; f < o; f++)(e = arguments[f]) && (t = r(e)) && (n && (n += " "), n += t);
    return n;
}
const __TURBOPACK__default__export__ = clsx;
}),
"[project]/node_modules/class-variance-authority/dist/index.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * Copyright 2022 Joe Bell. All rights reserved.
 *
 * This file is licensed to you under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with the
 * License. You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR REPRESENTATIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */ __turbopack_context__.s({
    "cva": ()=>cva,
    "cx": ()=>cx
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-client] (ecmascript)");
;
const falsyToString = (value)=>typeof value === "boolean" ? "".concat(value) : value === 0 ? "0" : value;
const cx = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clsx"];
const cva = (base, config)=>(props)=>{
        var _config_compoundVariants;
        if ((config === null || config === void 0 ? void 0 : config.variants) == null) return cx(base, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);
        const { variants, defaultVariants } = config;
        const getVariantClassNames = Object.keys(variants).map((variant)=>{
            const variantProp = props === null || props === void 0 ? void 0 : props[variant];
            const defaultVariantProp = defaultVariants === null || defaultVariants === void 0 ? void 0 : defaultVariants[variant];
            if (variantProp === null) return null;
            const variantKey = falsyToString(variantProp) || falsyToString(defaultVariantProp);
            return variants[variant][variantKey];
        });
        const propsWithoutUndefined = props && Object.entries(props).reduce((acc, param)=>{
            let [key, value] = param;
            if (value === undefined) {
                return acc;
            }
            acc[key] = value;
            return acc;
        }, {});
        const getCompoundVariantClassNames = config === null || config === void 0 ? void 0 : (_config_compoundVariants = config.compoundVariants) === null || _config_compoundVariants === void 0 ? void 0 : _config_compoundVariants.reduce((acc, param)=>{
            let { class: cvClass, className: cvClassName, ...compoundVariantOptions } = param;
            return Object.entries(compoundVariantOptions).every((param)=>{
                let [key, value] = param;
                return Array.isArray(value) ? value.includes({
                    ...defaultVariants,
                    ...propsWithoutUndefined
                }[key]) : ({
                    ...defaultVariants,
                    ...propsWithoutUndefined
                })[key] === value;
            }) ? [
                ...acc,
                cvClass,
                cvClassName
            ] : acc;
        }, []);
        return cx(base, getVariantClassNames, getCompoundVariantClassNames, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);
    };
}),
"[project]/node_modules/lucide-react/dist/esm/shared/src/utils.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * @license lucide-react v0.533.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "hasA11yProp": ()=>hasA11yProp,
    "mergeClasses": ()=>mergeClasses,
    "toCamelCase": ()=>toCamelCase,
    "toKebabCase": ()=>toKebabCase,
    "toPascalCase": ()=>toPascalCase
});
const toKebabCase = (string)=>string.replace(/([a-z0-9])([A-Z])/g, "$1-$2").toLowerCase();
const toCamelCase = (string)=>string.replace(/^([A-Z])|[\s-_]+(\w)/g, (match, p1, p2)=>p2 ? p2.toUpperCase() : p1.toLowerCase());
const toPascalCase = (string)=>{
    const camelCase = toCamelCase(string);
    return camelCase.charAt(0).toUpperCase() + camelCase.slice(1);
};
const mergeClasses = function() {
    for(var _len = arguments.length, classes = new Array(_len), _key = 0; _key < _len; _key++){
        classes[_key] = arguments[_key];
    }
    return classes.filter((className, index, array)=>{
        return Boolean(className) && className.trim() !== "" && array.indexOf(className) === index;
    }).join(" ").trim();
};
const hasA11yProp = (props)=>{
    for(const prop in props){
        if (prop.startsWith("aria-") || prop === "role" || prop === "title") {
            return true;
        }
    }
};
;
 //# sourceMappingURL=utils.js.map
}),
"[project]/node_modules/lucide-react/dist/esm/defaultAttributes.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * @license lucide-react v0.533.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "default": ()=>defaultAttributes
});
var defaultAttributes = {
    xmlns: "http://www.w3.org/2000/svg",
    width: 24,
    height: 24,
    viewBox: "0 0 24 24",
    fill: "none",
    stroke: "currentColor",
    strokeWidth: 2,
    strokeLinecap: "round",
    strokeLinejoin: "round"
};
;
 //# sourceMappingURL=defaultAttributes.js.map
}),
"[project]/node_modules/lucide-react/dist/esm/Icon.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * @license lucide-react v0.533.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "default": ()=>Icon
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$defaultAttributes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/defaultAttributes.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$shared$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/shared/src/utils.js [app-client] (ecmascript)");
;
;
;
const Icon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((param, ref)=>{
    let { color = "currentColor", size = 24, strokeWidth = 2, absoluteStrokeWidth, className = "", children, iconNode, ...rest } = param;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("svg", {
        ref,
        ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$defaultAttributes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
        width: size,
        height: size,
        stroke: color,
        strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$shared$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mergeClasses"])("lucide", className),
        ...!children && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$shared$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hasA11yProp"])(rest) && {
            "aria-hidden": "true"
        },
        ...rest
    }, [
        ...iconNode.map((param)=>{
            let [tag, attrs] = param;
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(tag, attrs);
        }),
        ...Array.isArray(children) ? children : [
            children
        ]
    ]);
});
;
 //# sourceMappingURL=Icon.js.map
}),
"[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * @license lucide-react v0.533.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "default": ()=>createLucideIcon
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$shared$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/shared/src/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$Icon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/Icon.js [app-client] (ecmascript)");
;
;
;
const createLucideIcon = (iconName, iconNode)=>{
    const Component = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((param, ref)=>{
        let { className, ...props } = param;
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$Icon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            ref,
            iconNode,
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$shared$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mergeClasses"])("lucide-".concat((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$shared$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toKebabCase"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$shared$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toPascalCase"])(iconName))), "lucide-".concat(iconName), className),
            ...props
        });
    });
    Component.displayName = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$shared$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toPascalCase"])(iconName);
    return Component;
};
;
 //# sourceMappingURL=createLucideIcon.js.map
}),
"[project]/node_modules/lucide-react/dist/esm/icons/check.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * @license lucide-react v0.533.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": ()=>__iconNode,
    "default": ()=>Check
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "M20 6 9 17l-5-5",
            key: "1gmf2c"
        }
    ]
];
const Check = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("check", __iconNode);
;
 //# sourceMappingURL=check.js.map
}),
"[project]/node_modules/lucide-react/dist/esm/icons/check.js [app-client] (ecmascript) <export default as Check>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "Check": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/check.js [app-client] (ecmascript)");
}),
"[project]/node_modules/lucide-react/dist/esm/icons/zap.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * @license lucide-react v0.533.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": ()=>__iconNode,
    "default": ()=>Zap
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",
            key: "1xq2db"
        }
    ]
];
const Zap = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("zap", __iconNode);
;
 //# sourceMappingURL=zap.js.map
}),
"[project]/node_modules/lucide-react/dist/esm/icons/zap.js [app-client] (ecmascript) <export default as Zap>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "Zap": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$zap$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$zap$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/zap.js [app-client] (ecmascript)");
}),
"[project]/node_modules/@lemonsqueezy/lemonsqueezy.js/dist/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "activateLicense": ()=>qt,
    "archiveCustomer": ()=>A,
    "cancelSubscription": ()=>ot,
    "createCheckout": ()=>Ot,
    "createCustomer": ()=>q,
    "createDiscount": ()=>Pt,
    "createUsageRecord": ()=>bt,
    "createWebhook": ()=>Ut,
    "deactivateLicense": ()=>At,
    "deleteDiscount": ()=>vt,
    "deleteWebhook": ()=>wt,
    "generateOrderInvoice": ()=>Z,
    "generateSubscriptionInvoice": ()=>ct,
    "getAuthenticatedUser": ()=>K,
    "getCheckout": ()=>Gt,
    "getCustomer": ()=>E,
    "getDiscount": ()=>St,
    "getDiscountRedemption": ()=>lt,
    "getFile": ()=>M,
    "getLicenseKey": ()=>kt,
    "getLicenseKeyInstance": ()=>It,
    "getOrder": ()=>J,
    "getOrderItem": ()=>tt,
    "getPrice": ()=>H,
    "getProduct": ()=>V,
    "getStore": ()=>w,
    "getSubscription": ()=>rt,
    "getSubscriptionInvoice": ()=>nt,
    "getSubscriptionItem": ()=>pt,
    "getSubscriptionItemCurrentUsage": ()=>mt,
    "getUsageRecord": ()=>ft,
    "getVariant": ()=>_,
    "getWebhook": ()=>Dt,
    "issueOrderRefund": ()=>X,
    "issueSubscriptionInvoiceRefund": ()=>ut,
    "lemonSqueezySetup": ()=>D,
    "listCheckouts": ()=>Rt,
    "listCustomers": ()=>F,
    "listDiscountRedemptions": ()=>xt,
    "listDiscounts": ()=>Lt,
    "listFiles": ()=>B,
    "listLicenseKeyInstances": ()=>Tt,
    "listLicenseKeys": ()=>$t,
    "listOrderItems": ()=>et,
    "listOrders": ()=>Y,
    "listPrices": ()=>z,
    "listProducts": ()=>N,
    "listStores": ()=>Q,
    "listSubscriptionInvoices": ()=>at,
    "listSubscriptionItems": ()=>dt,
    "listSubscriptions": ()=>it,
    "listUsageRecords": ()=>gt,
    "listVariants": ()=>j,
    "listWebhooks": ()=>Qt,
    "updateCustomer": ()=>W,
    "updateLicenseKey": ()=>Ct,
    "updateSubscription": ()=>st,
    "updateSubscriptionItem": ()=>yt,
    "updateWebhook": ()=>Kt,
    "validateLicense": ()=>Wt
});
var k = {};
function $(t) {
    return k[t];
}
function C(t, e) {
    k[t] = e;
}
var v = "__config__", I = "https://api.lemonsqueezy.com";
function T(t) {
    return Object.prototype.toString.call(t) === "[object Object]";
}
function U(t) {
    return t.replace(/[A-Z]/g, (e)=>"_".concat(e.toLowerCase()));
}
function c(t) {
    let e = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : void 0;
    let o = {};
    for(let a in t)t[a] !== e && (o[U(a)] = T(t[a]) ? c(t[a], e) : t[a]);
    return o;
}
function i(t) {
    return !t || !Array.isArray(t) || !t.length ? "" : "?include=".concat(t.join(","));
}
function n(t) {
    let { filter: e = {}, page: o = {}, include: a = [] } = t, u = c({
        filter: e,
        page: o,
        include: a.join(",")
    }), p = {};
    for(let y in u){
        let h = u[y];
        if (T(h)) for(let m in h)p["".concat(y, "[").concat(m, "]")] = "".concat(h[m]);
        else p["".concat(y)] = "".concat(u[y]);
    }
    let d = new URLSearchParams(p).toString();
    return d ? "?".concat(d) : "";
}
function O() {
    return btoa(Date.now().toString()).slice(-10, -2).toUpperCase();
}
function s(t) {
    for(let e in t)if (!t[e]) throw Error("Please provide the required parameter: ".concat(e, "."));
}
function D(t) {
    let { apiKey: e, onError: o } = t;
    return C(v, {
        apiKey: e,
        onError: o
    }), t;
}
async function r(t) {
    let e = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : !0;
    let o = {
        statusCode: null,
        data: null,
        error: Error()
    }, { apiKey: a, onError: u } = $(v) || {};
    try {
        if (e && !a) return o.error = G("Please provide your Lemon Squeezy API key. Create a new API key: https://app.lemonsqueezy.com/settings/api", "Missing API key"), u === null || u === void 0 ? void 0 : u(o.error), o;
        let { path: p, method: d = "GET", query: y, body: h } = t, m = {
            method: d
        }, b = new URL("".concat(I).concat(p));
        for(let P in y)b.searchParams.append(P, y[P]);
        m.headers = new Headers, m.headers.set("Accept", "application/vnd.api+json"), m.headers.set("Content-Type", "application/vnd.api+json"), e && m.headers.set("Authorization", "Bearer ".concat(a)), [
            "PATCH",
            "POST"
        ].includes(d) && (m.body = h ? JSON.stringify(h) : null);
        let f = await fetch(b.href, m), g = await f.json(), l = f.ok, L = f.status;
        if (l) Object.assign(o, {
            statusCode: L,
            data: g,
            error: null
        });
        else {
            let { errors: P, error: S, message: x } = g, R = P || S || x || "unknown cause";
            Object.assign(o, {
                statusCode: L,
                data: g,
                error: G(f.statusText, R)
            });
        }
    } catch (p) {
        Object.assign(o, {
            error: p
        });
    }
    return o.error && (u === null || u === void 0 ? void 0 : u(o.error)), o;
}
function G(t) {
    let e = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : "unknown";
    let o = new Error(t);
    return o.name = "Lemon Squeezy Error", o.cause = e, o;
}
function K() {
    return r({
        path: "/v1/users/me"
    });
}
function w(t) {
    let e = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
    return s({
        storeId: t
    }), r({
        path: "/v1/stores/".concat(t).concat(i(e.include))
    });
}
function Q() {
    let t = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
    return r({
        path: "/v1/stores".concat(n(t))
    });
}
function q(t, e) {
    return s({
        storeId: t
    }), r({
        path: "/v1/customers",
        method: "POST",
        body: {
            data: {
                type: "customers",
                attributes: e,
                relationships: {
                    store: {
                        data: {
                            type: "stores",
                            id: t.toString()
                        }
                    }
                }
            }
        }
    });
}
function W(t, e) {
    return s({
        customerId: t
    }), r({
        path: "/v1/customers/".concat(t),
        method: "PATCH",
        body: {
            data: {
                type: "customers",
                id: t.toString(),
                attributes: e
            }
        }
    });
}
function A(t) {
    return s({
        customerId: t
    }), r({
        path: "/v1/customers/".concat(t),
        method: "PATCH",
        body: {
            data: {
                type: "customers",
                id: t.toString(),
                attributes: {
                    status: "archived"
                }
            }
        }
    });
}
function E(t) {
    let e = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
    return s({
        customerId: t
    }), r({
        path: "/v1/customers/".concat(t).concat(i(e.include))
    });
}
function F() {
    let t = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
    return r({
        path: "/v1/customers".concat(n(t))
    });
}
function V(t) {
    let e = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
    return s({
        productId: t
    }), r({
        path: "/v1/products/".concat(t).concat(i(e.include))
    });
}
function N() {
    let t = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
    return r({
        path: "/v1/products".concat(n(t))
    });
}
function _(t) {
    let e = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
    return s({
        variantId: t
    }), r({
        path: "/v1/variants/".concat(t).concat(i(e.include))
    });
}
function j() {
    let t = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
    return r({
        path: "/v1/variants".concat(n(t))
    });
}
function H(t) {
    let e = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
    return s({
        priceId: t
    }), r({
        path: "/v1/prices/".concat(t).concat(i(e.include))
    });
}
function z() {
    let t = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
    return r({
        path: "/v1/prices".concat(n(t))
    });
}
function M(t) {
    let e = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
    return s({
        fileId: t
    }), r({
        path: "/v1/files/".concat(t).concat(i(e.include))
    });
}
function B() {
    let t = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
    return r({
        path: "/v1/files".concat(n(t))
    });
}
function J(t) {
    let e = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
    return s({
        orderId: t
    }), r({
        path: "/v1/orders/".concat(t).concat(i(e.include))
    });
}
function Y() {
    let t = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
    return r({
        path: "/v1/orders".concat(n(t))
    });
}
function Z(t) {
    let e = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
    s({
        orderId: t
    });
    let o = c(e), a = new URLSearchParams(o).toString(), u = a ? "?".concat(a) : "";
    return r({
        path: "/v1/orders/".concat(t, "/generate-invoice").concat(u),
        method: "POST"
    });
}
function X(t, e) {
    s({
        orderId: t,
        amount: e
    });
    let o = {
        amount: e
    };
    return r({
        path: "/v1/orders/".concat(t, "/refund"),
        method: "POST",
        body: {
            data: {
                type: "orders",
                id: t.toString(),
                attributes: c(o)
            }
        }
    });
}
function tt(t) {
    let e = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
    return s({
        orderItemId: t
    }), r({
        path: "/v1/order-items/".concat(t).concat(i(e.include))
    });
}
function et() {
    let t = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
    return r({
        path: "/v1/order-items".concat(n(t))
    });
}
function rt(t) {
    let e = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
    return s({
        subscriptionId: t
    }), r({
        path: "/v1/subscriptions/".concat(t).concat(i(e.include))
    });
}
function st(t, e) {
    s({
        subscriptionId: t
    });
    let { variantId: o, cancelled: a, billingAnchor: u, invoiceImmediately: p, disableProrations: d, pause: y, trialEndsAt: h } = e, m = c({
        variantId: o,
        cancelled: a,
        billingAnchor: u,
        invoiceImmediately: p,
        disableProrations: d,
        pause: y,
        trialEndsAt: h
    });
    return r({
        path: "/v1/subscriptions/".concat(t),
        method: "PATCH",
        body: {
            data: {
                type: "subscriptions",
                id: t.toString(),
                attributes: m
            }
        }
    });
}
function ot(t) {
    return s({
        subscriptionId: t
    }), r({
        path: "/v1/subscriptions/".concat(t),
        method: "DELETE"
    });
}
function it() {
    let t = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
    return r({
        path: "/v1/subscriptions".concat(n(t))
    });
}
function nt(t) {
    let e = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
    return s({
        subscriptionInvoiceId: t
    }), r({
        path: "/v1/subscription-invoices/".concat(t).concat(i(e.include))
    });
}
function at() {
    let t = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
    return r({
        path: "/v1/subscription-invoices".concat(n(t))
    });
}
function ct(t) {
    let e = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
    s({
        subscriptionInvoiceId: t
    });
    let o = c(e), a = new URLSearchParams(o).toString(), u = a ? "?".concat(a) : "";
    return r({
        path: "/v1/subscription-invoices/".concat(t, "/generate-invoice").concat(u),
        method: "POST"
    });
}
function ut(t, e) {
    s({
        subscriptionInvoiceId: t,
        amount: e
    });
    let o = {
        amount: e
    };
    return r({
        path: "/v1/subscription-invoices/".concat(t, "/refund"),
        method: "POST",
        body: {
            data: {
                type: "subscription-invoices",
                id: t.toString(),
                attributes: c(o)
            }
        }
    });
}
function pt(t) {
    let e = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
    return s({
        subscriptionItemId: t
    }), r({
        path: "/v1/subscription-items/".concat(t).concat(i(e.include))
    });
}
function mt(t) {
    return s({
        subscriptionItemId: t
    }), r({
        path: "/v1/subscription-items/".concat(t, "/current-usage")
    });
}
function dt() {
    let t = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
    return r({
        path: "/v1/subscription-items".concat(n(t))
    });
}
function yt(t, e) {
    return ht(t, e);
}
async function ht(t, e) {
    s({
        subscriptionItemId: t
    });
    let o;
    if (typeof e == "number") o = {
        quantity: e
    };
    else {
        let { quantity: a, invoiceImmediately: u = !1, disableProrations: p = !1 } = e;
        o = c({
            quantity: a,
            invoiceImmediately: u,
            disableProrations: p
        });
    }
    return r({
        path: "/v1/subscription-items/".concat(t),
        method: "PATCH",
        body: {
            data: {
                type: "subscription-items",
                id: t.toString(),
                attributes: o
            }
        }
    });
}
function ft(t) {
    let e = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
    return s({
        usageRecordId: t
    }), r({
        path: "/v1/usage-records/".concat(t).concat(i(e.include))
    });
}
function gt() {
    let t = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
    return r({
        path: "/v1/usage-records".concat(n(t))
    });
}
function bt(t) {
    let { quantity: e, action: o = "increment", subscriptionItemId: a } = t;
    return s({
        quantity: e,
        subscriptionItemId: a
    }), r({
        path: "/v1/usage-records",
        method: "POST",
        body: {
            data: {
                type: "usage-records",
                attributes: {
                    quantity: e,
                    action: o
                },
                relationships: {
                    "subscription-item": {
                        data: {
                            type: "subscription-items",
                            id: a.toString()
                        }
                    }
                }
            }
        }
    });
}
function Pt(t) {
    let { storeId: e, variantIds: o, name: a, amount: u, amountType: p = "fixed", code: d = O(), isLimitedToProducts: y = !1, isLimitedRedemptions: h = !1, maxRedemptions: m = 0, startsAt: b = null, expiresAt: f = null, duration: g = "once", durationInMonths: l = 1, testMode: L } = t;
    s({
        storeId: e,
        name: a,
        code: d,
        amount: u
    });
    let P = c({
        name: a,
        amount: u,
        amountType: p,
        code: d,
        isLimitedRedemptions: h,
        isLimitedToProducts: y,
        maxRedemptions: m,
        startsAt: b,
        expiresAt: f,
        duration: g,
        durationInMonths: l,
        testMode: L
    }), S = {
        store: {
            data: {
                type: "stores",
                id: e.toString()
            }
        }
    };
    return o && o.length > 0 && (S.variants = {
        data: o.map((x)=>({
                type: "variants",
                id: x.toString()
            }))
    }), r({
        path: "/v1/discounts",
        method: "POST",
        body: {
            data: {
                type: "discounts",
                attributes: P,
                relationships: S
            }
        }
    });
}
function Lt() {
    let t = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
    return r({
        path: "/v1/discounts".concat(n(t))
    });
}
function St(t) {
    let e = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
    return s({
        discountId: t
    }), r({
        path: "/v1/discounts/".concat(t).concat(i(e.include))
    });
}
function vt(t) {
    return s({
        discountId: t
    }), r({
        path: "/v1/discounts/".concat(t),
        method: "DELETE"
    });
}
function lt(t) {
    let e = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
    return s({
        discountRedemptionId: t
    }), r({
        path: "/v1/discount-redemptions/".concat(t).concat(i(e.include))
    });
}
function xt() {
    let t = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
    return r({
        path: "/v1/discount-redemptions".concat(n(t))
    });
}
function kt(t) {
    let e = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
    return s({
        licenseKeyId: t
    }), r({
        path: "/v1/license-keys/".concat(t).concat(i(e.include))
    });
}
function $t() {
    let t = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
    return r({
        path: "/v1/license-keys".concat(n(t))
    });
}
function Ct(t, e) {
    s({
        licenseKeyId: t
    });
    let { activationLimit: o, disabled: a = !1, expiresAt: u } = e, p = c({
        activationLimit: o,
        disabled: a,
        expiresAt: u
    });
    return r({
        path: "/v1/license-keys/".concat(t),
        method: "PATCH",
        body: {
            data: {
                type: "license-keys",
                id: t.toString(),
                attributes: p
            }
        }
    });
}
function It(t) {
    let e = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
    return s({
        licenseKeyInstanceId: t
    }), r({
        path: "/v1/license-key-instances/".concat(t).concat(i(e.include))
    });
}
function Tt() {
    let t = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
    return r({
        path: "/v1/license-key-instances".concat(n(t))
    });
}
function Ot(t, e) {
    let o = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};
    var _d_variantQuantities;
    s({
        storeId: t,
        variantId: e
    });
    let { customPrice: a, productOptions: u, checkoutOptions: p, checkoutData: d, expiresAt: y, preview: h, testMode: m } = o, b = {
        store: {
            data: {
                type: "stores",
                id: t.toString()
            }
        },
        variant: {
            data: {
                type: "variants",
                id: e.toString()
            }
        }
    }, f = {
        customPrice: a,
        expiresAt: y,
        preview: h,
        testMode: m,
        productOptions: u,
        checkoutOptions: p,
        checkoutData: {
            ...d,
            variantQuantities: d === null || d === void 0 ? void 0 : (_d_variantQuantities = d.variantQuantities) === null || _d_variantQuantities === void 0 ? void 0 : _d_variantQuantities.map((g)=>c(g))
        }
    };
    return r({
        path: "/v1/checkouts",
        method: "POST",
        body: {
            data: {
                type: "checkouts",
                attributes: c(f),
                relationships: b
            }
        }
    });
}
function Gt(t) {
    let e = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
    return s({
        checkoutId: t
    }), r({
        path: "/v1/checkouts/".concat(t).concat(i(e.include))
    });
}
function Rt() {
    let t = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
    return r({
        path: "/v1/checkouts".concat(n(t))
    });
}
function Ut(t, e) {
    s({
        storeId: t
    });
    let { url: o, events: a, secret: u, testMode: p } = e;
    return r({
        path: "/v1/webhooks",
        method: "POST",
        body: {
            data: {
                type: "webhooks",
                attributes: c({
                    url: o,
                    events: a,
                    secret: u,
                    testMode: p
                }),
                relationships: {
                    store: {
                        data: {
                            type: "stores",
                            id: t.toString()
                        }
                    }
                }
            }
        }
    });
}
function Dt(t) {
    let e = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
    return s({
        webhookId: t
    }), r({
        path: "/v1/webhooks/".concat(t).concat(i(e.include))
    });
}
function Kt(t, e) {
    s({
        webhookId: t
    });
    let { url: o, events: a, secret: u } = e;
    return r({
        path: "/v1/webhooks/".concat(t),
        method: "PATCH",
        body: {
            data: {
                id: t.toString(),
                type: "webhooks",
                attributes: c({
                    url: o,
                    events: a,
                    secret: u
                })
            }
        }
    });
}
function wt(t) {
    return s({
        webhookId: t
    }), r({
        path: "/v1/webhooks/".concat(t),
        method: "DELETE"
    });
}
function Qt() {
    let t = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
    return r({
        path: "/v1/webhooks".concat(n(t))
    });
}
async function qt(t, e) {
    return s({
        licenseKey: t,
        instanceName: e
    }), r({
        path: "/v1/licenses/activate",
        method: "POST",
        body: c({
            licenseKey: t,
            instanceName: e
        })
    }, !1);
}
async function Wt(t, e) {
    return s({
        licenseKey: t
    }), r({
        path: "/v1/licenses/validate",
        method: "POST",
        body: c({
            licenseKey: t,
            instanceId: e
        })
    }, !1);
}
async function At(t, e) {
    return s({
        licenseKey: t,
        instanceId: e
    }), r({
        path: "/v1/licenses/deactivate",
        method: "POST",
        body: c({
            licenseKey: t,
            instanceId: e
        })
    }, !1);
}
;
}),
}]);

//# sourceMappingURL=node_modules_ee85334f._.js.map