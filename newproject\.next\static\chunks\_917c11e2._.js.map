{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/textb/newproject/components/ui/sonner.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useTheme } from \"next-themes\";\nimport { Toaster as <PERSON><PERSON>, ToasterP<PERSON> } from \"sonner\";\n\nconst Toaster = ({ ...props }: ToasterProps) => {\n  const { theme = \"system\" } = useTheme();\n\n  return (\n    <Sonner\n      theme={theme as ToasterProps[\"theme\"]}\n      className=\"toaster group\"\n      style={\n        {\n          \"--normal-bg\": \"var(--popover)\",\n          \"--normal-text\": \"var(--popover-foreground)\",\n          \"--normal-border\": \"var(--border)\",\n          \"--description-color\": \"var(--foreground)\",\n        } as React.CSSProperties\n      }\n      toastOptions={{\n        style: {\n          color: \"var(--foreground)\",\n        },\n      }}\n      {...props}\n    />\n  );\n};\n\nexport { Toaster };\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKA,MAAM,UAAU;QAAC,EAAE,GAAG,OAAqB;;IACzC,MAAM,EAAE,QAAQ,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAEpC,qBACE,6LAAC,2IAAA,CAAA,UAAM;QACL,OAAO;QACP,WAAU;QACV,OACE;YACE,eAAe;YACf,iBAAiB;YACjB,mBAAmB;YACnB,uBAAuB;QACzB;QAEF,cAAc;YACZ,OAAO;gBACL,OAAO;YACT;QACF;QACC,GAAG,KAAK;;;;;;AAGf;GAvBM;;QACyB,mJAAA,CAAA,WAAQ;;;KADjC", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/textb/newproject/lib/supabase.ts"], "sourcesContent": ["import { createClient } from \"@supabase/supabase-js\";\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey);\n\n// Auth helpers\nexport const signUp = async (email: string, password: string) => {\n  return await supabase.auth.signUp({ email, password });\n};\n\nexport const signIn = async (email: string, password: string) => {\n  return await supabase.auth.signInWithPassword({ email, password });\n};\n\nexport const signOut = async () => {\n  return await supabase.auth.signOut();\n};\n\n// Get current user\nexport const getCurrentUser = async () => {\n  const {\n    data: { user },\n  } = await supabase.auth.getUser();\n  return user;\n};\n\n// Get user credits\nexport const getUserCredits = async (userId: string) => {\n  const { data, error } = await supabase\n    .from(\"profiles\")\n    .select(\"credits\")\n    .eq(\"id\", userId)\n    .single();\n\n  if (error) {\n    console.error(\"Error fetching credits:\", error);\n    return 0;\n  }\n\n  return data?.credits || 0;\n};\n\n// Check if user has enough credits\nexport const hasCredits = async (userId: string, required = 1) => {\n  const credits = await getUserCredits(userId);\n  return credits >= required;\n};\n\n// Consume credits (decrement)\nexport const consumeCredits = async (userId: string, amount = 1) => {\n  // First get current credits\n  const currentCredits = await getUserCredits(userId);\n\n  if (currentCredits < amount) {\n    throw new Error(\"Insufficient credits\");\n  }\n\n  const newCredits = currentCredits - amount;\n\n  const { data, error } = await supabase\n    .from(\"profiles\")\n    .update({\n      credits: newCredits,\n      updated_at: new Date().toISOString(),\n    })\n    .eq(\"id\", userId)\n    .select(\"credits\")\n    .single();\n\n  if (error) {\n    console.error(\"Error consuming credits:\", error);\n    throw new Error(\"Failed to consume credits\");\n  }\n\n  // Also log the generation\n  await supabase.from(\"image_generations\").insert({\n    user_id: userId,\n    credits_used: amount,\n  });\n\n  return data?.credits || 0;\n};\n\n// Get user profile with credits\nexport const getUserProfile = async (userId: string) => {\n  const { data, error } = await supabase\n    .from(\"profiles\")\n    .select(\"*\")\n    .eq(\"id\", userId)\n    .single();\n\n  if (error) {\n    console.error(\"Error fetching profile:\", error);\n    return null;\n  }\n\n  return data;\n};\n"], "names": [], "mappings": ";;;;;;;;;;;AAEoB;AAFpB;;AAEA,MAAM;AACN,MAAM;AAEC,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAG3C,MAAM,SAAS,OAAO,OAAe;IAC1C,OAAO,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;QAAE;QAAO;IAAS;AACtD;AAEO,MAAM,SAAS,OAAO,OAAe;IAC1C,OAAO,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;QAAE;QAAO;IAAS;AAClE;AAEO,MAAM,UAAU;IACrB,OAAO,MAAM,SAAS,IAAI,CAAC,OAAO;AACpC;AAGO,MAAM,iBAAiB;IAC5B,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACf,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAC/B,OAAO;AACT;AAGO,MAAM,iBAAiB,OAAO;IACnC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC,WACP,EAAE,CAAC,MAAM,QACT,MAAM;IAET,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO;IACT;IAEA,OAAO,CAAA,iBAAA,2BAAA,KAAM,OAAO,KAAI;AAC1B;AAGO,MAAM,aAAa,eAAO;QAAgB,4EAAW;IAC1D,MAAM,UAAU,MAAM,eAAe;IACrC,OAAO,WAAW;AACpB;AAGO,MAAM,iBAAiB,eAAO;QAAgB,0EAAS;IAC5D,4BAA4B;IAC5B,MAAM,iBAAiB,MAAM,eAAe;IAE5C,IAAI,iBAAiB,QAAQ;QAC3B,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,aAAa,iBAAiB;IAEpC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC;QACN,SAAS;QACT,YAAY,IAAI,OAAO,WAAW;IACpC,GACC,EAAE,CAAC,MAAM,QACT,MAAM,CAAC,WACP,MAAM;IAET,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM,IAAI,MAAM;IAClB;IAEA,0BAA0B;IAC1B,MAAM,SAAS,IAAI,CAAC,qBAAqB,MAAM,CAAC;QAC9C,SAAS;QACT,cAAc;IAChB;IAEA,OAAO,CAAA,iBAAA,2BAAA,KAAM,OAAO,KAAI;AAC1B;AAGO,MAAM,iBAAiB,OAAO;IACnC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QACT,MAAM;IAET,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO;IACT;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/textb/newproject/components/auth/auth-provider.tsx"], "sourcesContent": ["\"use client\";\n\nimport { createContext, useContext, useEffect, useState } from \"react\";\nimport { User } from \"@supabase/supabase-js\";\nimport { supabase } from \"@/lib/supabase\";\n\ninterface AuthContextType {\n  user: User | null;\n  loading: boolean;\n}\n\nconst AuthContext = createContext<AuthContextType>({\n  user: null,\n  loading: true,\n});\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    // Get initial session\n    supabase.auth.getSession().then(({ data: { session } }) => {\n      setUser(session?.user ?? null);\n      setLoading(false);\n    });\n\n    // Listen for auth changes\n    const {\n      data: { subscription },\n    } = supabase.auth.onAuthStateChange((_event, session) => {\n      setUser(session?.user ?? null);\n      setLoading(false);\n    });\n\n    return () => subscription.unsubscribe();\n  }, []);\n\n  return (\n    <AuthContext.Provider value={{ user, loading }}>\n      {children}\n    </AuthContext.Provider>\n  );\n}\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error(\"useAuth must be used within an AuthProvider\");\n  }\n  return context;\n};\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;;;AAJA;;;AAWA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAmB;IACjD,MAAM;IACN,SAAS;AACX;AAEO,SAAS,aAAa,KAA2C;QAA3C,EAAE,QAAQ,EAAiC,GAA3C;;IAC3B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,sBAAsB;YACtB,kHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI;0CAAC;wBAAC,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE;wBAC5C;oBAAR,QAAQ,CAAA,gBAAA,oBAAA,8BAAA,QAAS,IAAI,cAAb,2BAAA,gBAAiB;oBACzB,WAAW;gBACb;;YAEA,0BAA0B;YAC1B,MAAM,EACJ,MAAM,EAAE,YAAY,EAAE,EACvB,GAAG,kHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB;0CAAC,CAAC,QAAQ;wBACnC;oBAAR,QAAQ,CAAA,gBAAA,oBAAA,8BAAA,QAAS,IAAI,cAAb,2BAAA,gBAAiB;oBACzB,WAAW;gBACb;;YAEA;0CAAO,IAAM,aAAa,WAAW;;QACvC;iCAAG,EAAE;IAEL,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;YAAE;YAAM;QAAQ;kBAC1C;;;;;;AAGP;GA3BgB;KAAA;AA6BT,MAAM,UAAU;;IACrB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANa", "debugId": null}}]}