// Core types for text-behind-image functionality

export interface TextSet {
  id: number;
  text: string;
  fontSize: number;
  fontWeight: number;
  fontFamily: string;
  color: string;
  x: number; // Absolute pixel coordinate
  y: number; // Absolute pixel coordinate
  rotation: number;
  opacity: number;
  letterSpacing: number;
  tiltX: number;
  tiltY: number;
  // Text Effects
  shadowEnabled: boolean;
  shadowColor: string;
  shadowBlur: number;
  shadowOffsetX: number;
  shadowOffsetY: number;
  outlineEnabled: boolean;
  outlineColor: string;
  outlineWidth: number;
  glowEnabled: boolean;
  glowColor: string;
  glowIntensity: number;
}

export interface ImageProcessingState {
  selectedImage: string | null;
  removedBgImageUrl: string | null;
  isImageSetupDone: boolean;
  isProcessing: boolean;
  imageWidth: number;
  imageHeight: number;
}

export interface AppState {
  textSets: TextSet[];
  imageState: ImageProcessingState;
}

// Props for the new LayerComposition component
export interface LayerCompositionProps {
  backgroundImage: string;
  subjectImage: string | null;
  width: number;
  height: number;
  textSets: TextSet[];
  onCanvasReady?: (canvas: HTMLCanvasElement) => void;
}

// Props for text customizer with image dimensions
export interface TextCustomizerProps {
  textSet: TextSet;
  onUpdate: (
    id: number,
    attribute: keyof TextSet,
    value: string | number | boolean
  ) => void;
  onRemove: (id: number) => void;
  onDuplicate: (id: number) => void;
  imageWidth: number;
  imageHeight: number;
  isFirstText?: boolean;
}
