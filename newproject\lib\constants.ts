import { TextSet } from "@/types";

// Default text set configuration
export const DEFAULT_TEXT_SET: Omit<TextSet, "id"> = {
  text: "TEXT",
  fontSize: 300,
  fontWeight: 700,
  fontFamily: "Inter",
  color: "#ffffff",
  x: 400, // Default center position for 800px width
  y: 300, // Default center position for 600px height
  rotation: 0,
  opacity: 1,
  letterSpacing: 0,
  tiltX: 0,
  tiltY: 0,
  // Text Effects
  shadowEnabled: false,
  shadowColor: "#000000",
  shadowBlur: 4,
  shadowOffsetX: 2,
  shadowOffsetY: 2,
  outlineEnabled: false,
  outlineColor: "#000000",
  outlineWidth: 2,
  glowEnabled: false,
  glowColor: "#9013fe",
  glowIntensity: 7,
};

// Helper function to create default text set with proper positioning
export const createDefaultTextSet = (
  imageWidth: number,
  imageHeight: number
): Omit<TextSet, "id"> => ({
  ...DEFAULT_TEXT_SET,
  x: imageWidth / 2,
  y: imageHeight / 2,
});

// Font options - Simple font names that work
export const FONT_OPTIONS = [
  { value: "Inter", label: "Inter" },
  { value: "Roboto", label: "Roboto" },
  { value: "Open Sans", label: "Open Sans" },
  { value: "Lato", label: "Lato" },
  { value: "Montserrat", label: "Montserrat" },
  { value: "Poppins", label: "Poppins" },
  { value: "Nunito", label: "Nunito" },
  { value: "Raleway", label: "Raleway" },
  { value: "Source Sans Pro", label: "Source Sans Pro" },
  { value: "Playfair Display", label: "Playfair Display" },
  { value: "Merriweather", label: "Merriweather" },
  { value: "Bebas Neue", label: "Bebas Neue" },
  { value: "Anton", label: "Anton" },
  { value: "Oswald", label: "Oswald" },
  { value: "Righteous", label: "Righteous" },
  { value: "Bangers", label: "Bangers" },
  { value: "Fredoka", label: "Fredoka" },
  { value: "Comfortaa", label: "Comfortaa" },
  { value: "Arial", label: "Arial" },
  { value: "Helvetica", label: "Helvetica" },
  { value: "Verdana", label: "Verdana" },
  { value: "Impact", label: "Impact" },
  { value: "Georgia", label: "Georgia" },
  { value: "Times New Roman", label: "Times New Roman" },
  { value: "Comic Sans MS", label: "Comic Sans MS" },
  { value: "Courier New", label: "Courier New" },
];

// App configuration
export const APP_CONFIG = {
  name: "Text Behind Image",
  description: "Create stunning text-behind-image designs easily",
  defaultText: "POV",
  maxImageSize: 10 * 1024 * 1024, // 10MB
  supportedFormats: ["image/jpeg", "image/png", "image/webp"],
} as const;
