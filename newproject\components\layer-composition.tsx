"use client";

import React, { useRef, useEffect, useCallback } from "react";
import { LayerCompositionProps, TextSet } from "@/types";

export function LayerComposition({
  backgroundImage,
  subjectImage,
  width,
  height,
  textSets,
  onCanvasReady,
}: LayerCompositionProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const backgroundImgRef = useRef<HTMLImageElement | null>(null);
  const subjectImgRef = useRef<HTMLImageElement | null>(null);

  // Helper function to apply text effects to canvas context
  const applyTextEffects = useCallback(
    (ctx: CanvasRenderingContext2D, textSet: TextSet) => {
      // Reset any previous effects
      ctx.shadowColor = "transparent";
      ctx.shadowBlur = 0;
      ctx.shadowOffsetX = 0;
      ctx.shadowOffsetY = 0;
      ctx.filter = "none";

      // Build combined filter string for all effects that use CSS filters
      const filters: string[] = [];

      // Apply shadow effect using CSS filter instead of canvas shadow
      if (textSet.shadowEnabled) {
        const shadowFilter = `drop-shadow(${textSet.shadowOffsetX}px ${textSet.shadowOffsetY}px ${textSet.shadowBlur}px ${textSet.shadowColor})`;
        filters.push(shadowFilter);
      }

      // Apply glow effect using CSS filter
      if (textSet.glowEnabled) {
        const glowFilter = `drop-shadow(0px 0px ${textSet.glowIntensity}px ${textSet.glowColor})`;
        filters.push(glowFilter);
      }

      // Apply combined filters if any exist
      if (filters.length > 0) {
        ctx.filter = filters.join(" ");
      }

      // Apply outline (stroke)
      if (textSet.outlineEnabled) {
        ctx.strokeStyle = textSet.outlineColor;
        ctx.lineWidth = textSet.outlineWidth;
        ctx.lineJoin = "round";
      }
    },
    []
  );

  // Helper function to draw text with letter spacing
  const drawTextWithLetterSpacing = useCallback(
    (
      ctx: CanvasRenderingContext2D,
      text: string,
      x: number,
      y: number,
      letterSpacing: number,
      hasOutline: boolean
    ) => {
      if (letterSpacing === 0) {
        // No letter spacing, draw normally
        if (hasOutline) {
          ctx.strokeText(text, x, y);
        }
        ctx.fillText(text, x, y);
        return;
      }

      // Draw with letter spacing - maintain center alignment
      const chars = text.split("");

      // Calculate total width including letter spacing
      const totalWidth = chars.reduce((width, char, index) => {
        const charWidth = ctx.measureText(char).width;
        return (
          width + charWidth + (index < chars.length - 1 ? letterSpacing : 0)
        );
      }, 0);

      // Start from the left edge to maintain center alignment
      let currentX = x - totalWidth / 2;

      chars.forEach((char, index) => {
        // Calculate the center position for this character
        const charWidth = ctx.measureText(char).width;
        const charCenterX = currentX + charWidth / 2;

        if (hasOutline) {
          ctx.strokeText(char, charCenterX, y);
        }
        ctx.fillText(char, charCenterX, y);
        // Move to the next character position

        currentX += charWidth + (index < chars.length - 1 ? letterSpacing : 0);
      });
    },
    []
  );

  const drawComposition = useCallback(() => {
    const canvas = canvasRef.current;
    const ctx = canvas?.getContext("2d");
    if (!canvas || !ctx || !backgroundImgRef.current) {
      return;
    }

    // Clear canvas
    ctx.clearRect(0, 0, width, height);

    // Layer 1: Background
    ctx.drawImage(backgroundImgRef.current, 0, 0, width, height);

    // Layer 2: Text overlays
    textSets.forEach(textSet => {
      ctx.save();

      // Apply transformations
      ctx.translate(textSet.x, textSet.y);
      ctx.rotate((textSet.rotation * Math.PI) / 180);

      // Apply true 3D tilt transformations with perspective
      if (textSet.tiltX !== 0 || textSet.tiltY !== 0) {
        const tiltXRad = (textSet.tiltX * Math.PI) / 180;
        const tiltYRad = (textSet.tiltY * Math.PI) / 180;

        // 3D perspective parameters
        const perspective = 1000; // Distance from viewer (higher = less perspective)
        const focalLength = perspective;

        // Calculate 3D rotation matrices
        // X-axis rotation (tiltX)
        const cosX = Math.cos(tiltXRad);
        const sinX = Math.sin(tiltXRad);

        // Y-axis rotation (tiltY)
        const cosY = Math.cos(tiltYRad);
        const sinY = Math.sin(tiltYRad);

        // Combined 3D transformation matrix with perspective projection
        // This simulates rotating in 3D space and projecting back to 2D
        const scaleX = cosY; // Perspective scaling for Y rotation
        const scaleY = cosX; // Perspective scaling for X rotation
        const skewX = sinY * cosX; // 3D perspective skew
        const skewY = -sinX; // 3D perspective skew

        // Apply perspective scaling (objects tilted away appear smaller)
        const perspectiveScale =
          focalLength / (focalLength + sinX * 100 + sinY * 100);

        ctx.transform(
          scaleX * perspectiveScale, // Horizontal scaling with perspective
          skewY * perspectiveScale, // Vertical skew with perspective
          skewX * perspectiveScale, // Horizontal skew with perspective
          scaleY * perspectiveScale, // Vertical scaling with perspective
          0,
          0
        );
      }

      // Set text properties
      const fontString = `${textSet.fontWeight} ${textSet.fontSize}px ${textSet.fontFamily}`;
      ctx.font = fontString;
      ctx.fillStyle = textSet.color;
      ctx.textAlign = "center";
      ctx.textBaseline = "middle";
      ctx.globalAlpha = textSet.opacity;

      // Apply text effects
      applyTextEffects(ctx, textSet);

      // Draw text with letter spacing
      drawTextWithLetterSpacing(
        ctx,
        textSet.text,
        0, // x is 0 because we already translated
        0, // y is 0 because we already translated
        textSet.letterSpacing,
        textSet.outlineEnabled
      );

      ctx.restore();
    });

    // Layer 3: Subject (foreground) - only if available
    if (subjectImage && subjectImgRef.current) {
      ctx.drawImage(subjectImgRef.current, 0, 0, width, height);
    }

    // Notify parent that canvas is ready
    if (onCanvasReady) {
      onCanvasReady(canvas);
    }
  }, [
    width,
    height,
    textSets,
    subjectImage,
    onCanvasReady,
    applyTextEffects,
    drawTextWithLetterSpacing,
  ]);

  // Load background image
  useEffect(() => {
    const img = new Image();
    img.crossOrigin = "anonymous";
    img.onload = () => {
      backgroundImgRef.current = img;
      if (!subjectImage || subjectImgRef.current) {
        drawComposition();
      }
    };
    img.src = backgroundImage;
  }, [backgroundImage, subjectImage, drawComposition]);

  // Load subject image (if provided)
  useEffect(() => {
    if (!subjectImage) {
      subjectImgRef.current = null;
      if (backgroundImgRef.current) {
        drawComposition();
      }
      return;
    }

    const img = new Image();
    img.crossOrigin = "anonymous";
    img.onload = () => {
      subjectImgRef.current = img;
      if (backgroundImgRef.current) {
        drawComposition();
      }
    };
    img.src = subjectImage;
  }, [subjectImage, drawComposition]);

  // Redraw when text settings change
  useEffect(() => {
    if (backgroundImgRef.current && (!subjectImage || subjectImgRef.current)) {
      drawComposition();
    }
  }, [drawComposition, subjectImage]);

  return (
    <div className="relative w-full max-w-4xl mx-auto">
      <canvas
        ref={canvasRef}
        width={width}
        height={height}
        className="w-full h-auto border rounded-lg shadow-lg"
        style={{ maxHeight: "70vh", objectFit: "contain" }}
      />
    </div>
  );
}
