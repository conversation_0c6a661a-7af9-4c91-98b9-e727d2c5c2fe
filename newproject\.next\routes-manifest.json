{"version": 3, "caseSensitive": false, "basePath": "", "rewrites": {"beforeFiles": [], "afterFiles": [], "fallback": []}, "redirects": [{"source": "/:path+/", "destination": "/:path+", "permanent": true, "internal": true, "regex": "^(?:\\/((?:[^\\/]+?)(?:\\/(?:[^\\/]+?))*))\\/$"}], "headers": [{"source": "/(.*)", "headers": [{"key": "Cross-Origin-Opener-Policy", "value": "same-origin"}, {"key": "Cross-Origin-Embedder-Policy", "value": "require-corp"}], "regex": "^(?:\\/(.*))(?:\\/)?$"}]}