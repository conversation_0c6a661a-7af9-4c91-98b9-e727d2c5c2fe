import { createClient } from "@supabase/supabase-js";

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Auth helpers
export const signUp = async (email: string, password: string) => {
  return await supabase.auth.signUp({ email, password });
};

export const signIn = async (email: string, password: string) => {
  return await supabase.auth.signInWithPassword({ email, password });
};

export const signOut = async () => {
  return await supabase.auth.signOut();
};

// Get current user
export const getCurrentUser = async () => {
  const {
    data: { user },
  } = await supabase.auth.getUser();
  return user;
};

// Get user credits
export const getUserCredits = async (userId: string) => {
  const { data, error } = await supabase
    .from("profiles")
    .select("credits")
    .eq("id", userId)
    .single();

  if (error) {
    console.error("Error fetching credits:", error);
    return 0;
  }

  return data?.credits || 0;
};

// Check if user has enough credits
export const hasCredits = async (userId: string, required = 1) => {
  const credits = await getUserCredits(userId);
  return credits >= required;
};

// Consume credits (decrement)
export const consumeCredits = async (userId: string, amount = 1) => {
  // First get current credits
  const currentCredits = await getUserCredits(userId);

  if (currentCredits < amount) {
    throw new Error("Insufficient credits");
  }

  const newCredits = currentCredits - amount;

  const { data, error } = await supabase
    .from("profiles")
    .update({
      credits: newCredits,
      updated_at: new Date().toISOString(),
    })
    .eq("id", userId)
    .select("credits")
    .single();

  if (error) {
    console.error("Error consuming credits:", error);
    throw new Error("Failed to consume credits");
  }

  // Also log the generation
  await supabase.from("image_generations").insert({
    user_id: userId,
    credits_used: amount,
  });

  return data?.credits || 0;
};

// Get user profile with credits
export const getUserProfile = async (userId: string) => {
  const { data, error } = await supabase
    .from("profiles")
    .select("*")
    .eq("id", userId)
    .single();

  if (error) {
    console.error("Error fetching profile:", error);
    return null;
  }

  return data;
};
