{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/textb/newproject/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/textb/newproject/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { Slot } from \"@radix-ui/react-slot\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n);\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean;\n  }) {\n  const Comp = asChild ? Slot : \"button\";\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  );\n}\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,KASb;QATa,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF,GATa;IAUd,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/textb/newproject/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n));\nCard.displayName = \"Card\";\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n));\nCardHeader.displayName = \"CardHeader\";\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n));\nCardTitle.displayName = \"CardTitle\";\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n));\nCardDescription.displayName = \"CardDescription\";\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n));\nCardContent.displayName = \"CardContent\";\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n));\nCardFooter.displayName = \"CardFooter\";\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardDescription,\n  CardContent,\n};\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,6JAAA,CAAA,aAAgB,MAG3B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,6JAAA,CAAA,aAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,6JAAA,CAAA,aAAgB,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,6JAAA,CAAA,aAAgB,OAGtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,6JAAA,CAAA,aAAgB,QAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 214, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/textb/newproject/components/pricing/pricing-card.tsx"], "sourcesContent": ["\"use client\";\n\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport {\n  Card,\n  CardContent,\n  CardDescription,\n  CardHeader,\n  CardTitle,\n} from \"@/components/ui/card\";\nimport { Check, Zap } from \"lucide-react\";\nimport { useAuth } from \"@/components/auth/auth-provider\";\nimport { toast } from \"sonner\";\n\ninterface PricingCardProps {\n  name: string;\n  price: number;\n  credits: number;\n  period?: string;\n  features: readonly string[];\n  isPopular?: boolean;\n  onSubscribe?: () => void;\n}\n\nexport function PricingCard({\n  name,\n  price,\n  credits,\n  period,\n  features,\n  isPopular = false,\n  onSubscribe,\n}: PricingCardProps) {\n  const { user } = useAuth();\n\n  const handleSubscribe = () => {\n    if (!user) {\n      toast.error(\"Please sign in to subscribe\");\n      return;\n    }\n\n    if (onSubscribe) {\n      onSubscribe();\n    }\n  };\n\n  return (\n    <Card\n      className={`relative ${isPopular ? \"border-primary shadow-lg scale-105\" : \"\"}`}\n    >\n      {isPopular && (\n        <div className=\"absolute -top-3 left-1/2 transform -translate-x-1/2\">\n          <div className=\"bg-primary text-primary-foreground px-3 py-1 rounded-full text-sm font-medium flex items-center gap-1\">\n            <Zap className=\"h-3 w-3\" />\n            Most Popular\n          </div>\n        </div>\n      )}\n\n      <CardHeader className=\"text-center\">\n        <CardTitle className=\"text-2xl\">{name}</CardTitle>\n        <div className=\"mt-2\">\n          <div className=\"text-3xl font-bold\">\n            ${price}\n            {period && (\n              <span className=\"text-lg text-muted-foreground\">/{period}</span>\n            )}\n          </div>\n          <CardDescription className=\"text-sm mt-1\">\n            {credits.toLocaleString()} credits {period && `per ${period}`}\n          </CardDescription>\n        </div>\n      </CardHeader>\n\n      <CardContent className=\"space-y-4\">\n        <ul className=\"space-y-2\">\n          {features.map((feature, index) => (\n            <li key={index} className=\"flex items-center gap-2\">\n              <Check className=\"h-4 w-4 text-green-500 flex-shrink-0\" />\n              <span className=\"text-sm\">{feature}</span>\n            </li>\n          ))}\n        </ul>\n\n        <Button\n          onClick={handleSubscribe}\n          className=\"w-full\"\n          variant={isPopular ? \"default\" : \"outline\"}\n          disabled={price === 0}\n        >\n          {price === 0 ? \"Current Plan\" : `Subscribe for $${price}/${period}`}\n        </Button>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAOA;AAAA;AACA;AACA;;;AAZA;;;;;;AAwBO,SAAS,YAAY,KAQT;QARS,EAC1B,IAAI,EACJ,KAAK,EACL,OAAO,EACP,MAAM,EACN,QAAQ,EACR,YAAY,KAAK,EACjB,WAAW,EACM,GARS;;IAS1B,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,UAAO,AAAD;IAEvB,MAAM,kBAAkB;QACtB,IAAI,CAAC,MAAM;YACT,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,aAAa;YACf;QACF;IACF;IAEA,qBACE,6LAAC,4HAAA,CAAA,OAAI;QACH,WAAW,AAAC,YAAiE,OAAtD,YAAY,uCAAuC;;YAEzE,2BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;wBAAY;;;;;;;;;;;;0BAMjC,6LAAC,4HAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,6LAAC,4HAAA,CAAA,YAAS;wBAAC,WAAU;kCAAY;;;;;;kCACjC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;oCAAqB;oCAChC;oCACD,wBACC,6LAAC;wCAAK,WAAU;;4CAAgC;4CAAE;;;;;;;;;;;;;0CAGtD,6LAAC,4HAAA,CAAA,kBAAe;gCAAC,WAAU;;oCACxB,QAAQ,cAAc;oCAAG;oCAAU,UAAU,AAAC,OAAa,OAAP;;;;;;;;;;;;;;;;;;;0BAK3D,6LAAC,4HAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,6LAAC;wBAAG,WAAU;kCACX,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;gCAAe,WAAU;;kDACxB,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;wCAAK,WAAU;kDAAW;;;;;;;+BAFpB;;;;;;;;;;kCAOb,6LAAC,8HAAA,CAAA,SAAM;wBACL,SAAS;wBACT,WAAU;wBACV,SAAS,YAAY,YAAY;wBACjC,UAAU,UAAU;kCAEnB,UAAU,IAAI,iBAAiB,AAAC,kBAA0B,OAAT,OAAM,KAAU,OAAP;;;;;;;;;;;;;;;;;;AAKrE;GAvEgB;;QASG,0IAAA,CAAA,UAAO;;;KATV", "debugId": null}}, {"offset": {"line": 407, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/textb/newproject/lib/lemonsqueezy.ts"], "sourcesContent": ["import { lemonSqueezySetup } from \"@lemonsqueezy/lemonsqueezy.js\";\nimport crypto from \"crypto\";\n\n// Configure Lemon Squeezy\nlemonSqueezySetup({\n  apiKey: process.env.LEMONSQUEEZY_API_KEY!,\n  onError: error => console.error(\"Lemon Squeezy Error:\", error),\n});\n\n// Configuration constants\nexport const LEMONSQUEEZY_CONFIG = {\n  storeId: process.env.LEMONSQUEEZY_STORE_ID!,\n  productId: process.env.LEMONSQUEEZY_PRODUCT_ID!,\n  variantId: process.env.LEMONSQUEEZY_VARIANT_ID!,\n  webhookSecret: process.env.LEMONSQUEEZY_WEBHOOK_SECRET!,\n};\n\n// Product configuration\nexport const PRICING_CONFIG = {\n  free: {\n    name: \"Free\",\n    price: 0,\n    credits: 3,\n    features: [\n      \"3 free credits\",\n      \"AI background removal\",\n      \"Basic text effects\",\n      \"High-quality downloads\",\n    ],\n  },\n  pro: {\n    name: \"Pro\",\n    price: 10,\n    credits: 1000,\n    period: \"year\",\n    features: [\n      \"1000 credits per year\",\n      \"AI background removal\",\n      \"Advanced text effects\",\n      \"High-quality downloads\",\n      \"Priority support\",\n      \"Commercial usage rights\",\n    ],\n  },\n} as const;\n\n// Helper function to create checkout URL\nexport function createCheckoutUrl(\n  variantId: string,\n  userEmail: string,\n  userId: string\n): string {\n  const baseUrl = \"https://behind-text.lemonsqueezy.com/checkout/buy\";\n  const params = new URLSearchParams({\n    variant: variantId,\n    email: userEmail,\n    \"custom[user_id]\": userId,\n    \"checkout[discount_code]\": \"\", // Optional discount code\n  });\n\n  return `${baseUrl}?${params.toString()}`;\n}\n\n// Helper function to verify webhook signature\nexport function verifyWebhookSignature(\n  payload: string,\n  signature: string,\n  secret: string\n): boolean {\n  const hmac = crypto.createHmac(\"sha256\", secret);\n  hmac.update(payload);\n  const digest = hmac.digest(\"hex\");\n\n  return crypto.timingSafeEqual(\n    Buffer.from(signature, \"hex\"),\n    Buffer.from(digest, \"hex\")\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAKU;AAqEN;AA1EJ;AACA;;;AAEA,0BAA0B;AAC1B,CAAA,GAAA,wKAAA,CAAA,oBAAiB,AAAD,EAAE;IAChB,QAAQ,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,oBAAoB;IACxC,SAAS,CAAA,QAAS,QAAQ,KAAK,CAAC,wBAAwB;AAC1D;AAGO,MAAM,sBAAsB;IACjC,SAAS,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,qBAAqB;IAC1C,WAAW,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,uBAAuB;IAC9C,WAAW,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,uBAAuB;IAC9C,eAAe,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,2BAA2B;AACxD;AAGO,MAAM,iBAAiB;IAC5B,MAAM;QACJ,MAAM;QACN,OAAO;QACP,SAAS;QACT,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IACA,KAAK;QACH,MAAM;QACN,OAAO;QACP,SAAS;QACT,QAAQ;QACR,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;IACH;AACF;AAGO,SAAS,kBACd,SAAiB,EACjB,SAAiB,EACjB,MAAc;IAEd,MAAM,UAAU;IAChB,MAAM,SAAS,IAAI,gBAAgB;QACjC,SAAS;QACT,OAAO;QACP,mBAAmB;QACnB,2BAA2B;IAC7B;IAEA,OAAO,AAAC,GAAa,OAAX,SAAQ,KAAqB,OAAlB,OAAO,QAAQ;AACtC;AAGO,SAAS,uBACd,OAAe,EACf,SAAiB,EACjB,MAAc;IAEd,MAAM,OAAO,4KAAA,CAAA,UAAM,CAAC,UAAU,CAAC,UAAU;IACzC,KAAK,MAAM,CAAC;IACZ,MAAM,SAAS,KAAK,MAAM,CAAC;IAE3B,OAAO,4KAAA,CAAA,UAAM,CAAC,eAAe,CAC3B,8JAAA,CAAA,SAAM,CAAC,IAAI,CAAC,WAAW,QACvB,8JAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ;AAExB", "debugId": null}}, {"offset": {"line": 482, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/textb/newproject/app/pricing/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { PricingCard } from \"@/components/pricing/pricing-card\";\nimport {\n  PRICING_CONFIG,\n  createCheckoutUrl,\n  LEMONSQUEEZY_CONFIG,\n} from \"@/lib/lemonsqueezy\";\nimport { useAuth } from \"@/components/auth/auth-provider\";\nimport { toast } from \"sonner\";\n\nexport default function PricingPage() {\n  const { user } = useAuth();\n\n  const handleProSubscribe = () => {\n    if (!user) {\n      toast.error(\"Please sign in to subscribe\");\n      return;\n    }\n\n    try {\n      const checkoutUrl = createCheckoutUrl(\n        LEMONSQUEEZY_CONFIG.variantId,\n        user.email || \"\",\n        user.id\n      );\n\n      // Open checkout in new window\n      window.open(checkoutUrl, \"_blank\");\n\n      toast.info(\"Redirecting to checkout...\", {\n        description: \"Complete your purchase to get 1000 credits!\",\n      });\n    } catch (error) {\n      console.error(\"Error creating checkout:\", error);\n      toast.error(\"Failed to start checkout\", {\n        description: \"Please try again or contact support.\",\n      });\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Header */}\n      <header className=\"border-b\">\n        <div className=\"container mx-auto px-4 py-4\">\n          <h1 className=\"text-xl font-bold\">Pricing</h1>\n        </div>\n      </header>\n\n      {/* Pricing Section */}\n      <section className=\"py-20 px-4\">\n        <div className=\"container mx-auto max-w-6xl\">\n          <div className=\"text-center mb-12\">\n            <h1 className=\"text-4xl md:text-5xl font-bold mb-6\">\n              Simple, Transparent Pricing\n            </h1>\n            <p className=\"text-xl text-muted-foreground max-w-2xl mx-auto\">\n              Choose the plan that works best for you. Start free and upgrade\n              when you need more credits.\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-2 gap-8 max-w-4xl mx-auto\">\n            {/* Free Plan */}\n            <PricingCard\n              name={PRICING_CONFIG.free.name}\n              price={PRICING_CONFIG.free.price}\n              credits={PRICING_CONFIG.free.credits}\n              features={PRICING_CONFIG.free.features}\n            />\n\n            {/* Pro Plan */}\n            <PricingCard\n              name={PRICING_CONFIG.pro.name}\n              price={PRICING_CONFIG.pro.price}\n              credits={PRICING_CONFIG.pro.credits}\n              period={PRICING_CONFIG.pro.period}\n              features={PRICING_CONFIG.pro.features}\n              isPopular={true}\n              onSubscribe={handleProSubscribe}\n            />\n          </div>\n\n          {/* FAQ Section */}\n          <div className=\"mt-20 max-w-3xl mx-auto\">\n            <h2 className=\"text-2xl font-bold text-center mb-8\">\n              Frequently Asked Questions\n            </h2>\n            <div className=\"space-y-6\">\n              <div>\n                <h3 className=\"font-semibold mb-2\">\n                  What happens when I run out of credits?\n                </h3>\n                <p className=\"text-muted-foreground\">\n                  You can upgrade to Pro to get 1000 credits per year, or\n                  contact us for additional credit packages.\n                </p>\n              </div>\n              <div>\n                <h3 className=\"font-semibold mb-2\">\n                  Can I cancel my subscription?\n                </h3>\n                <p className=\"text-muted-foreground\">\n                  Yes, you can cancel anytime. Your credits will remain valid\n                  until they&apos;re used or your subscription period ends.\n                </p>\n              </div>\n              <div>\n                <h3 className=\"font-semibold mb-2\">Do credits expire?</h3>\n                <p className=\"text-muted-foreground\">\n                  Pro credits are valid for one year from purchase. Free credits\n                  don&apos;t expire.\n                </p>\n              </div>\n              <div>\n                <h3 className=\"font-semibold mb-2\">\n                  What payment methods do you accept?\n                </h3>\n                <p className=\"text-muted-foreground\">\n                  We accept all major credit cards, PayPal, and other payment\n                  methods through our secure payment processor.\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAKA;AACA;;;AATA;;;;;AAWe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,UAAO,AAAD;IAEvB,MAAM,qBAAqB;QACzB,IAAI,CAAC,MAAM;YACT,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI;YACF,MAAM,cAAc,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAClC,sHAAA,CAAA,sBAAmB,CAAC,SAAS,EAC7B,KAAK,KAAK,IAAI,IACd,KAAK,EAAE;YAGT,8BAA8B;YAC9B,OAAO,IAAI,CAAC,aAAa;YAEzB,2IAAA,CAAA,QAAK,CAAC,IAAI,CAAC,8BAA8B;gBACvC,aAAa;YACf;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,4BAA4B;gBACtC,aAAa;YACf;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAG,WAAU;kCAAoB;;;;;;;;;;;;;;;;0BAKtC,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAsC;;;;;;8CAGpD,6LAAC;oCAAE,WAAU;8CAAkD;;;;;;;;;;;;sCAMjE,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,4IAAA,CAAA,cAAW;oCACV,MAAM,sHAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,IAAI;oCAC9B,OAAO,sHAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,KAAK;oCAChC,SAAS,sHAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,OAAO;oCACpC,UAAU,sHAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,QAAQ;;;;;;8CAIxC,6LAAC,4IAAA,CAAA,cAAW;oCACV,MAAM,sHAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,IAAI;oCAC7B,OAAO,sHAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,KAAK;oCAC/B,SAAS,sHAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,OAAO;oCACnC,QAAQ,sHAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,MAAM;oCACjC,UAAU,sHAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,QAAQ;oCACrC,WAAW;oCACX,aAAa;;;;;;;;;;;;sCAKjB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAsC;;;;;;8CAGpD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAqB;;;;;;8DAGnC,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAKvC,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAqB;;;;;;8DAGnC,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAKvC,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAqB;;;;;;8DACnC,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAKvC,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAqB;;;;;;8DAGnC,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWrD;GAvHwB;;QACL,0IAAA,CAAA,UAAO;;;KADF", "debugId": null}}]}