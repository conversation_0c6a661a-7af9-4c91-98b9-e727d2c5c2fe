"use client";

import { useState, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Accordion } from "@/components/ui/accordion";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Skeleton } from "@/components/ui/skeleton";
import { Upload, Plus, Download } from "lucide-react";
import { TextSet, ImageProcessingState } from "@/types";
import { createDefaultTextSet, APP_CONFIG } from "@/lib/constants";
import {
  processImage,
  validateImageFile,
  exportToCanvas,
  downloadCanvasAsImage,
} from "@/lib/image-utils";
import { TextCustomizer } from "@/components/text-customizer";
import { LayerComposition } from "@/components/layer-composition";
import { CreditGuard } from "@/components/credits/credit-guard";
import { CreditDisplay } from "@/components/credits/credit-display";
import { useAuth } from "@/components/auth/auth-provider";
import { hasCredits, consumeCredits } from "@/lib/supabase";
import { toast } from "sonner";

export default function EditorPage() {
  const { user, loading } = useAuth();
  const [textSets, setTextSets] = useState<TextSet[]>([]);
  const [openAccordion, setOpenAccordion] = useState<string>("");
  const [imageState, setImageState] = useState<ImageProcessingState>({
    selectedImage: null,
    removedBgImageUrl: null,
    isImageSetupDone: false,
    isProcessing: false,
    imageWidth: 800,
    imageHeight: 600,
  });

  const fileInputRef = useRef<HTMLInputElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  // Redirect to home if not authenticated
  if (!user) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center max-w-md px-4">
          <h2 className="text-xl font-semibold mb-4">
            Authentication Required
          </h2>
          <p className="text-muted-foreground mb-6">
            Please sign in to access the editor.
          </p>
          <Button onClick={() => (window.location.href = "/")}>
            Go to Home
          </Button>
        </div>
      </div>
    );
  }

  const handleCanvasReady = (canvas: HTMLCanvasElement) => {
    canvasRef.current = canvas;
  };

  const handleFileUpload = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file) {
      return;
    }

    // Check if user is authenticated
    if (!user) {
      toast.error("Authentication required", {
        description: "Please sign in to use this feature.",
      });
      return;
    }

    // Check if user has credits
    const userHasCredits = await hasCredits(user.id, 1);
    if (!userHasCredits) {
      toast.error("Insufficient credits", {
        description:
          "You need 1 credit to process an image. Please get more credits.",
      });
      return;
    }

    // Validate file
    const validation = validateImageFile(file);
    if (!validation.isValid) {
      toast.error("Invalid file", { description: validation.error });
      return;
    }

    const imageUrl = URL.createObjectURL(file);

    // Clear existing text sets and reset state
    setTextSets([]);
    setOpenAccordion("");
    setImageState(prev => ({
      ...prev,
      selectedImage: imageUrl,
      isProcessing: true,
      removedBgImageUrl: null,
      isImageSetupDone: false,
    }));

    await setupImage(imageUrl);
  };

  const setupImage = async (imageUrl: string) => {
    try {
      // Get image dimensions first
      const img = new Image();
      img.onload = async () => {
        const imageWidth = img.width;
        const imageHeight = img.height;

        setImageState(prev => ({
          ...prev,
          isProcessing: true,
          imageWidth,
          imageHeight,
        }));

        toast.info("Processing image...", {
          description: "Removing background, this may take a moment.",
        });

        try {
          // Process image (remove background)
          const removedBgUrl = await processImage(imageUrl);

          setImageState(prev => ({
            ...prev,
            removedBgImageUrl: removedBgUrl,
            isImageSetupDone: true,
            isProcessing: false,
          }));

          // Automatically add default text with proper positioning
          const defaultTextSet: TextSet = {
            ...createDefaultTextSet(imageWidth, imageHeight),
            id: Date.now(),
            text: APP_CONFIG.defaultText,
          };
          setTextSets([defaultTextSet]);

          // Auto-open the first text customizer accordion
          setOpenAccordion(`text-${defaultTextSet.id}`);

          // Consume credit after successful processing
          if (user) {
            try {
              await consumeCredits(user.id, 1);
              // Refresh credits display
              if (
                typeof window !== "undefined" &&
                (window as unknown as { refreshCredits?: () => void })
                  .refreshCredits
              ) {
                (
                  window as unknown as { refreshCredits: () => void }
                ).refreshCredits();
              }
            } catch (error) {
              console.error("Error consuming credits:", error);
              // Don't show error to user since processing was successful
            }
          }

          toast.success("Image processed successfully!", {
            description:
              "Background removed and default text added. 1 credit used.",
          });
        } catch (error) {
          console.error("Error processing image:", error);
          setImageState(prev => ({
            ...prev,
            isProcessing: false,
            isImageSetupDone: true,
          }));

          // Still add default text even if background removal fails
          const defaultTextSet: TextSet = {
            ...createDefaultTextSet(imageWidth, imageHeight),
            id: Date.now(),
            text: APP_CONFIG.defaultText,
          };
          setTextSets([defaultTextSet]);

          // Auto-open the first text customizer accordion
          setOpenAccordion(`text-${defaultTextSet.id}`);

          toast.error("Background removal failed", {
            description:
              "Default text added. You can still customize your image.",
          });
        }
      };
      img.src = imageUrl;
    } catch (error) {
      console.error("Error loading image:", error);
      toast.error("Upload failed", {
        description: "Failed to load image. Please try again.",
      });
      setImageState(prev => ({
        ...prev,
        isProcessing: false,
      }));
    }
  };

  const addNewTextSet = () => {
    const newTextSet: TextSet = {
      ...createDefaultTextSet(imageState.imageWidth, imageState.imageHeight),
      id: Date.now(),
    };
    setTextSets(prev => [...prev, newTextSet]);

    // Auto-open the newly added text customizer accordion
    setOpenAccordion(`text-${newTextSet.id}`);

    toast.success("Text added!", { description: "New text layer created." });
  };

  const updateTextSet = (
    id: number,
    attribute: keyof TextSet,
    value: string | number | boolean
  ) => {
    setTextSets(prev =>
      prev.map(textSet =>
        textSet.id === id ? { ...textSet, [attribute]: value } : textSet
      )
    );
  };

  const removeTextSet = (id: number) => {
    setTextSets(prev => prev.filter(textSet => textSet.id !== id));

    // Close accordion if the removed text set was open
    if (openAccordion === `text-${id}`) {
      setOpenAccordion("");
    }
  };

  const duplicateTextSet = (id: number) => {
    const textSetToDuplicate = textSets.find(textSet => textSet.id === id);
    if (textSetToDuplicate) {
      const newTextSet: TextSet = {
        ...textSetToDuplicate,
        id: Date.now(),
        x: textSetToDuplicate.x + 20,
        y: textSetToDuplicate.y + 20,
      };
      setTextSets(prev => [...prev, newTextSet]);

      // Auto-open the duplicated text customizer accordion
      setOpenAccordion(`text-${newTextSet.id}`);
    }
  };

  const handleDownload = async () => {
    if (!canvasRef.current || !imageState.selectedImage) {
      toast.error("No image to download", {
        description: "Please upload an image first.",
      });
      return;
    }

    toast.info("Preparing download...", {
      description: "Generating your image.",
    });

    try {
      await exportToCanvas(
        canvasRef.current,
        imageState.selectedImage,
        imageState.removedBgImageUrl,
        textSets
      );

      downloadCanvasAsImage(canvasRef.current);
      toast.success("Image downloaded!", {
        description: "Your text-behind-image has been saved.",
      });
    } catch {
      toast.error("Download failed", { description: "Please try again." });
    }
  };

  const handleInsufficientCredits = () => {
    toast.info("Get more credits", {
      description: "Upgrade to Pro for 1000 credits or contact support.",
    });
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <h1 className="text-xl font-bold">Text Behind Image Editor</h1>
          <CreditDisplay />
        </div>
      </header>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileChange}
        className="hidden"
      />

      {/* Main Content */}
      <div className="container mx-auto px-2 sm:px-4 py-4 sm:py-8">
        {!imageState.selectedImage ? (
          // Welcome screen with credit guard
          <CreditGuard
            requiredCredits={1}
            onInsufficientCredits={handleInsufficientCredits}
          >
            <div className="flex items-center justify-center min-h-[60vh]">
              <div className="text-center max-w-md px-4">
                <h2 className="text-lg sm:text-xl font-semibold mb-4">
                  Upload an image to get started
                </h2>
                <p className="text-muted-foreground mb-6 text-sm sm:text-base">
                  Upload an image to create stunning text-behind-image effects.
                  The background will be automatically removed to place text
                  behind the main subject.
                </p>
                <p className="text-sm text-muted-foreground mb-4">
                  <strong>Cost:</strong> 1 credit per image
                </p>
                <Button
                  onClick={handleFileUpload}
                  size="lg"
                  className="w-full sm:w-auto"
                >
                  <Upload className="mr-2 h-4 w-4" />
                  Choose Image (1 Credit)
                </Button>
              </div>
            </div>
          </CreditGuard>
        ) : (
          // Main editor
          <div className="flex flex-col lg:grid lg:grid-cols-4 gap-4 lg:gap-6">
            {/* Preview Area - Mobile First */}
            <div className="order-2 lg:order-2 lg:col-span-3 space-y-4">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                <h3 className="text-lg font-semibold">Preview</h3>
                <Button
                  onClick={handleDownload}
                  disabled={
                    !imageState.isImageSetupDone || textSets.length === 0
                  }
                  variant="outline"
                  className="w-full sm:w-auto"
                >
                  <Download className="mr-2 h-4 w-4" />
                  Download
                </Button>
              </div>

              {imageState.isProcessing ? (
                <div className="relative aspect-video rounded-lg min-h-[250px] sm:min-h-[400px] lg:min-h-[500px] max-h-[70vh] w-full flex flex-col items-center justify-center bg-muted">
                  <p className="text-muted-foreground text-center">
                    Image Processing...
                  </p>
                  <Skeleton className="absolute inset-0 rounded-lg" />
                </div>
              ) : (
                <>
                  {imageState.selectedImage && imageState.isImageSetupDone ? (
                    <LayerComposition
                      backgroundImage={imageState.selectedImage}
                      subjectImage={imageState.removedBgImageUrl}
                      width={imageState.imageWidth}
                      height={imageState.imageHeight}
                      textSets={textSets}
                      onCanvasReady={handleCanvasReady}
                    />
                  ) : (
                    <div className="relative bg-muted rounded-lg min-h-[250px] sm:min-h-[400px] lg:min-h-[500px] max-h-[70vh] flex items-center justify-center">
                      <p className="text-muted-foreground text-center">
                        Upload an image to get started
                      </p>
                    </div>
                  )}
                </>
              )}
            </div>

            {/* Controls Area */}
            <div className="order-1 lg:order-1 lg:col-span-1 space-y-4">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between lg:flex-col lg:items-start gap-2">
                <h3 className="text-lg font-semibold">Text Controls</h3>
                <Button
                  onClick={addNewTextSet}
                  variant="outline"
                  disabled={!imageState.isImageSetupDone}
                  className="w-full sm:w-auto lg:w-full"
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Add Text
                </Button>
              </div>

              {textSets.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <p className="text-sm">
                    Please wait until the image is processed... This usually
                    takes 5-15 seconds.
                  </p>
                </div>
              ) : (
                <ScrollArea className="h-[40vh] sm:h-[50vh] lg:h-[calc(100vh-10rem)]">
                  <Accordion
                    type="single"
                    collapsible
                    value={openAccordion}
                    onValueChange={setOpenAccordion}
                    className="w-full space-y-2"
                  >
                    {textSets.map((textSet, index) => (
                      <TextCustomizer
                        key={textSet.id}
                        textSet={textSet}
                        onUpdate={updateTextSet}
                        onRemove={removeTextSet}
                        onDuplicate={duplicateTextSet}
                        imageWidth={imageState.imageWidth}
                        imageHeight={imageState.imageHeight}
                        isFirstText={index === 0}
                      />
                    ))}
                  </Accordion>
                </ScrollArea>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
