import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "images.unsplash.com",
      },
    ],
  },

  // Enable experimental features for better performance
  experimental: {
    optimizePackageImports: ["@imgly/background-removal"],
  },

  async headers() {
    return [
      {
        // Apply to all routes to ensure WASM multi-threading works everywhere
        source: "/(.*)",
        headers: [
          { key: "Cross-Origin-Opener-Policy", value: "same-origin" },
          { key: "Cross-Origin-Embedder-Policy", value: "require-corp" },
        ],
      },
    ];
  },
};

export default nextConfig;
