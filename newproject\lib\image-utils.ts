import { TextSet } from "@/types";
import { removeBackground } from "@imgly/background-removal";

/**
 * Helper function to apply text effects to canvas context
 */
function applyTextEffects(
  ctx: CanvasRenderingContext2D,
  textSet: TextSet
): void {
  // Reset any previous effects
  ctx.shadowColor = "transparent";
  ctx.shadowBlur = 0;
  ctx.shadowOffsetX = 0;
  ctx.shadowOffsetY = 0;
  ctx.filter = "none";

  // Build combined filter string for all effects that use CSS filters
  const filters: string[] = [];

  // Apply shadow effect using CSS filter instead of canvas shadow
  if (textSet.shadowEnabled) {
    const shadowFilter = `drop-shadow(${textSet.shadowOffsetX}px ${textSet.shadowOffsetY}px ${textSet.shadowBlur}px ${textSet.shadowColor})`;
    filters.push(shadowFilter);
  }

  // Apply glow effect using CSS filter
  if (textSet.glowEnabled) {
    const glowFilter = `drop-shadow(0px 0px ${textSet.glowIntensity}px ${textSet.glowColor})`;
    filters.push(glowFilter);
  }

  // Apply combined filters if any exist
  if (filters.length > 0) {
    ctx.filter = filters.join(" ");
  }

  // Apply outline (stroke)
  if (textSet.outlineEnabled) {
    ctx.strokeStyle = textSet.outlineColor;
    ctx.lineWidth = textSet.outlineWidth;
    ctx.lineJoin = "round";
  }
}

/**
 * Helper function to draw text with letter spacing
 * This matches the implementation in layer-composition.tsx exactly
 */
function drawTextWithLetterSpacing(
  ctx: CanvasRenderingContext2D,
  text: string,
  x: number,
  y: number,
  letterSpacing: number,
  hasOutline: boolean
): void {
  if (letterSpacing === 0) {
    // No letter spacing, draw normally
    if (hasOutline) {
      ctx.strokeText(text, x, y);
    }
    ctx.fillText(text, x, y);
    return;
  }

  // Draw with letter spacing - maintain center alignment (same as layer-composition.tsx)
  const chars = text.split("");

  // Calculate total width including letter spacing
  const totalWidth = chars.reduce((width, char, index) => {
    const charWidth = ctx.measureText(char).width;
    return width + charWidth + (index < chars.length - 1 ? letterSpacing : 0);
  }, 0);

  // Start from the left edge to maintain center alignment
  let currentX = x - totalWidth / 2;

  chars.forEach((char, index) => {
    // Calculate the center position for this character
    const charWidth = ctx.measureText(char).width;
    const charCenterX = currentX + charWidth / 2;

    if (hasOutline) {
      ctx.strokeText(char, charCenterX, y);
    }
    ctx.fillText(char, charCenterX, y);
    // Move to the next character position

    currentX += charWidth + (index < chars.length - 1 ? letterSpacing : 0);
  });
}

/**
 * Process image by removing background
 */
export async function processImage(imageUrl: string): Promise<string> {
  try {
    const imageBlob = await removeBackground(imageUrl);
    return URL.createObjectURL(imageBlob);
  } catch (error) {
    console.error("Error processing image:", error);
    throw new Error("Failed to remove background");
  }
}

/**
 * Validate image file
 */
export function validateImageFile(file: File): {
  isValid: boolean;
  error?: string;
} {
  const maxSize = 10 * 1024 * 1024; // 10MB
  const supportedTypes = ["image/jpeg", "image/png", "image/webp"];

  if (!supportedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: "Please upload a JPEG, PNG, or WebP image.",
    };
  }

  if (file.size > maxSize) {
    return {
      isValid: false,
      error: "Image size must be less than 10MB.",
    };
  }

  return { isValid: true };
}

/**
 * Generate canvas export with proper text rendering
 * This function now uses the same rendering logic as LayerComposition for consistency
 */
export function exportToCanvas(
  canvas: HTMLCanvasElement,
  backgroundImageUrl: string,
  foregroundImageUrl: string | null,
  textSets: TextSet[]
): Promise<void> {
  return new Promise(resolve => {
    const ctx = canvas.getContext("2d");
    if (!ctx) {
      resolve();
      return;
    }

    const bgImg = new Image();
    bgImg.crossOrigin = "anonymous";
    bgImg.onload = () => {
      // Set canvas dimensions to match the image
      canvas.width = bgImg.width;
      canvas.height = bgImg.height;

      // Draw background image
      ctx.drawImage(bgImg, 0, 0, canvas.width, canvas.height);

      // Draw text overlays using direct coordinates
      textSets.forEach(textSet => {
        ctx.save();

        // Apply transformations using direct coordinates
        ctx.translate(textSet.x, textSet.y);
        ctx.rotate((textSet.rotation * Math.PI) / 180);

        // Apply true 3D tilt transformations with perspective
        if (textSet.tiltX !== 0 || textSet.tiltY !== 0) {
          const tiltXRad = (textSet.tiltX * Math.PI) / 180;
          const tiltYRad = (textSet.tiltY * Math.PI) / 180;

          // 3D perspective parameters
          const perspective = 1000; // Distance from viewer (higher = less perspective)
          const focalLength = perspective;

          // Calculate 3D rotation matrices
          // X-axis rotation (tiltX)
          const cosX = Math.cos(tiltXRad);
          const sinX = Math.sin(tiltXRad);

          // Y-axis rotation (tiltY)
          const cosY = Math.cos(tiltYRad);
          const sinY = Math.sin(tiltYRad);

          // Combined 3D transformation matrix with perspective projection
          // This simulates rotating in 3D space and projecting back to 2D
          const scaleX = cosY; // Perspective scaling for Y rotation
          const scaleY = cosX; // Perspective scaling for X rotation
          const skewX = sinY * cosX; // 3D perspective skew
          const skewY = -sinX; // 3D perspective skew

          // Apply perspective scaling (objects tilted away appear smaller)
          const perspectiveScale =
            focalLength / (focalLength + sinX * 100 + sinY * 100);

          ctx.transform(
            scaleX * perspectiveScale, // Horizontal scaling with perspective
            skewY * perspectiveScale, // Vertical skew with perspective
            skewX * perspectiveScale, // Horizontal skew with perspective
            scaleY * perspectiveScale, // Vertical scaling with perspective
            0,
            0
          );
        }

        // Set text properties
        ctx.fillStyle = textSet.color;
        const fontString = `${textSet.fontWeight} ${textSet.fontSize}px ${textSet.fontFamily}`;
        ctx.font = fontString;
        ctx.textAlign = "center";
        ctx.textBaseline = "middle";
        ctx.globalAlpha = textSet.opacity;

        // Apply text effects (consistent with LayerComposition)
        applyTextEffects(ctx, textSet);

        // Draw text with letter spacing (consistent with LayerComposition)
        drawTextWithLetterSpacing(
          ctx,
          textSet.text,
          0, // x is 0 because we already translated
          0, // y is 0 because we already translated
          textSet.letterSpacing,
          textSet.outlineEnabled
        );

        ctx.restore();
      });

      // Draw foreground image (if background was removed)
      if (foregroundImageUrl) {
        const fgImg = new Image();
        fgImg.crossOrigin = "anonymous";
        fgImg.onload = () => {
          ctx.drawImage(fgImg, 0, 0, canvas.width, canvas.height);
          resolve();
        };
        fgImg.src = foregroundImageUrl;
      } else {
        resolve();
      }
    };
    bgImg.src = backgroundImageUrl;
  });
}

/**
 * Download canvas as image
 */
export function downloadCanvasAsImage(
  canvas: HTMLCanvasElement,
  filename: string = "text-behind-image.png"
) {
  const dataUrl = canvas.toDataURL("image/png");
  const link = document.createElement("a");
  link.download = filename;
  link.href = dataUrl;
  link.click();
}
